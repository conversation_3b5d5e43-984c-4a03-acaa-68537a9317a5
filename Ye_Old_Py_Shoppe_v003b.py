
# Ye Old Py Shoppe v0.03b – The Tome Begins
# Requires: pip install customtkinter

import os, zipfile, hashlib
import customtkinter as ctk
from tkinter import filedialog
from pathlib import Path

BATCH_SIZE = 25

class YeOldPyShoppe(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("Ye Old Py Shoppe v0.03b – The Tome Begins")
        self.geometry("1200x800")
        self.resizable(False, False)

        self.results = []
        self.batch_index = 0
        self.whisper_queue = []

        ctk.CTkLabel(self, text="Ye Old Py Shoppe", font=("Segoe UI", 24)).pack(pady=10)
        ctk.CTkButton(self, text="Add Folder (recursive)", command=self.scan_folder).pack(pady=5)
        self.preview_frame = ctk.CTkScrollableFrame(self, width=1100, height=600)
        self.preview_frame.pack(padx=10, pady=10)

        self.load_more_button = ctk.CTkButton(self, text="Load More", command=self.load_more_results)
        self.load_more_button.pack(pady=10)

    def scan_folder(self):
        folder = filedialog.askdirectory()
        if not folder: return
        self.results.clear()
        self.batch_index = 0
        self.preview_frame._parent_canvas.yview_moveto(0)
        for root, dirs, files in os.walk(folder):
            for file in files:
                if file.endswith(".py"):
                    full_path = os.path.join(root, file)
                    self.results.append({"type": "file", "path": full_path})
                elif file.endswith(".zip"):
                    zip_path = os.path.join(root, file)
                    self.scan_zip(zip_path)
        self.load_more_results()

    def scan_zip(self, zip_path):
        try:
            with zipfile.ZipFile(zip_path, 'r') as z:
                for zip_info in z.infolist():
                    if zip_info.filename.endswith(".py") and not zip_info.is_dir():
                        extracted = z.read(zip_info.filename).decode("utf-8", errors="ignore")
                        fake_path = f"archive://{zip_path}!{zip_info.filename}"
                        self.results.append({"type": "zip", "path": fake_path, "code": extracted})
        except Exception as e:
            self.results.append({"type": "error", "path": zip_path, "error": str(e)})

    def load_more_results(self):
        end = self.batch_index + BATCH_SIZE
        batch = self.results[self.batch_index:end]
        for item in batch:
            self.preview_result(item)
        self.batch_index += BATCH_SIZE
        if self.batch_index >= len(self.results):
            self.load_more_button.configure(state="disabled")

    def preview_result(self, item):
        box = ctk.CTkFrame(self.preview_frame)
        box.pack(padx=5, pady=5, fill="x")
        if item.get("type") == "error":
            ctk.CTkLabel(box, text=f"❌ Error: {item['path']}", text_color="red").pack(anchor="w")
            ctk.CTkLabel(box, text=item["error"]).pack(anchor="w")
        else:
            label = f"{item['path']}"
            ctk.CTkLabel(box, text=label, font=("Consolas", 12)).pack(anchor="w")
            try:
                if item["type"] == "file":
                    with open(item["path"], "r", encoding="utf-8", errors="ignore") as f:
                        code = f.read()
                else:
                    code = item["code"]
                # Whisper check
                if not code.strip().endswith(":") and "def " not in code and "class " not in code:
                    self.whisper_queue.append({"path": item["path"], "code": code})
                display = code[:300] + ("..." if len(code) > 300 else "")
                txt = ctk.CTkTextbox(box, height=80, wrap="word")
                txt.insert("1.0", display)
                txt.configure(state="disabled")
                txt.pack(padx=5, pady=5)
                hash_label = self.get_file_hash_display(item)
                if hash_label:
                    ctk.CTkLabel(box, text=hash_label, font=("Consolas", 10), text_color="gray").pack(anchor="w")
            except Exception as e:
                ctk.CTkLabel(box, text=f"Error reading: {e}", text_color="red").pack(anchor="w")

    def get_file_hash_display(self, item):
        try:
            if item["type"] == "file":
                h = hashlib.sha256()
                with open(item["path"], "rb") as f:
                    h.update(f.read())
                size = os.path.getsize(item["path"])
                return f"SHA256: {h.hexdigest()[:12]}... | Size: {size} bytes"
            elif item["type"] == "zip":
                h = hashlib.sha256(item["code"].encode("utf-8")).hexdigest()
                return f"SHA256: {h[:12]}... | Size: {len(item['code'])} bytes"
        except:
            return None

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    app = YeOldPyShoppe()
    app.mainloop()
