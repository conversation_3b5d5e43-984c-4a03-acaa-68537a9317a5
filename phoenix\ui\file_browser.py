"""
File browser component with FileZilla-style interface.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import logging
from pathlib import Path
from typing import Optional, List, TYPE_CHECKING

if TYPE_CHECKING:
    from .main_window import MainWindow
    from ..core.code_analyzer import FileAnalysis

logger = logging.getLogger(__name__)


class FileBrowser(ttk.Frame):
    """File browser with tree view and file list (FileZilla style)."""
    
    def __init__(self, parent, main_window: 'MainWindow', side: str = "left"):
        super().__init__(parent)
        self.main_window = main_window
        self.side = side
        self.current_path: Optional[Path] = None
        
        self._setup_ui()
        self._setup_bindings()
        
        # Initialize with home directory
        self.navigate_to(Path.home())
    
    def _setup_ui(self):
        """Set up the file browser UI."""
        # Path navigation bar
        nav_frame = ttk.Frame(self)
        nav_frame.pack(fill=tk.X, padx=2, pady=2)
        
        # Up button
        self.up_button = ttk.Button(nav_frame, text="↑", width=3, command=self.go_up)
        self.up_button.pack(side=tk.LEFT, padx=2)
        
        # Path entry
        self.path_var = tk.StringVar()
        self.path_entry = ttk.Entry(nav_frame, textvariable=self.path_var)
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)
        self.path_entry.bind("<Return>", self.on_path_enter)
        
        # Refresh button
        self.refresh_button = ttk.Button(nav_frame, text="⟳", width=3, command=self.refresh)
        self.refresh_button.pack(side=tk.RIGHT, padx=2)
        
        # Main content area with paned window
        paned = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Directory tree (left side)
        tree_frame = ttk.Frame(paned)
        paned.add(tree_frame, weight=1)
        
        ttk.Label(tree_frame, text="Folders").pack(anchor=tk.W)
        
        # Tree view for directories
        self.tree = ttk.Treeview(tree_frame, show="tree")
        tree_scroll = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=tree_scroll.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # File list (right side)
        list_frame = ttk.Frame(paned)
        paned.add(list_frame, weight=2)
        
        ttk.Label(list_frame, text="Files").pack(anchor=tk.W)
        
        # File listbox with details
        columns = ("name", "size", "modified", "type")
        self.file_list = ttk.Treeview(list_frame, columns=columns, show="tree headings")
        
        # Configure columns
        self.file_list.heading("#0", text="")
        self.file_list.column("#0", width=20, minwidth=20)
        self.file_list.heading("name", text="Name")
        self.file_list.column("name", width=200, minwidth=100)
        self.file_list.heading("size", text="Size")
        self.file_list.column("size", width=80, minwidth=50)
        self.file_list.heading("modified", text="Modified")
        self.file_list.column("modified", width=120, minwidth=80)
        self.file_list.heading("type", text="Type")
        self.file_list.column("type", width=80, minwidth=50)
        
        # Scrollbars for file list
        list_scroll_y = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_list.yview)
        list_scroll_x = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.file_list.xview)
        self.file_list.configure(yscrollcommand=list_scroll_y.set, xscrollcommand=list_scroll_x.set)
        
        self.file_list.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        list_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        list_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Context menu
        self.context_menu = tk.Menu(self, tearoff=0)
        self._setup_context_menu()
    
    def _setup_context_menu(self):
        """Set up the context menu for files."""
        self.context_menu.add_command(label="Analyze File", command=self.analyze_selected_file)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Add to Bucket...", command=self.add_to_bucket)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Add Comments", command=self.add_comments)
        self.context_menu.add_command(label="Strip Comments", command=self.strip_comments)
        self.context_menu.add_command(label="Undo Changes", command=self.undo_changes)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Open in Editor", command=self.open_in_editor)
        self.context_menu.add_command(label="Show in Explorer", command=self.show_in_explorer)
    
    def _setup_bindings(self):
        """Set up event bindings."""
        # Tree selection
        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)
        self.tree.bind("<Double-1>", self.on_tree_double_click)
        
        # File list selection and double-click
        self.file_list.bind("<Double-1>", self.on_file_double_click)
        self.file_list.bind("<Button-3>", self.on_file_right_click)  # Right-click
        
        # Keyboard shortcuts
        self.file_list.bind("<Return>", self.on_file_double_click)
        self.bind_all("<F5>", lambda e: self.refresh())
    
    def navigate_to(self, path: Path):
        """Navigate to a specific path."""
        try:
            if not path.exists():
                messagebox.showerror("Error", f"Path does not exist: {path}")
                return
            
            self.current_path = path.resolve()
            self.path_var.set(str(self.current_path))
            
            # Update tree and file list
            self._populate_tree()
            self._populate_file_list()
            
            # Update main window state
            if self.side == "left":
                self.main_window.current_left_path = self.current_path
            else:
                self.main_window.current_right_path = self.current_path
                
        except Exception as e:
            logger.error(f"Failed to navigate to {path}: {e}")
            messagebox.showerror("Error", f"Failed to navigate to path: {e}")
    
    def _populate_tree(self):
        """Populate the directory tree."""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        if not self.current_path:
            return
        
        try:
            # Add parent directories
            current = self.current_path
            items = []
            
            # Build path hierarchy
            while current.parent != current:  # Stop at root
                items.append(current)
                current = current.parent
            items.append(current)  # Add root
            
            # Add items to tree (reverse order for proper hierarchy)
            parent_id = ""
            for path in reversed(items):
                item_id = self.tree.insert(parent_id, "end", text=path.name or str(path), 
                                         values=[str(path)])
                parent_id = item_id
            
            # Add subdirectories of current path
            if self.current_path.is_dir():
                for item in sorted(self.current_path.iterdir()):
                    if item.is_dir() and not item.name.startswith('.'):
                        self.tree.insert(parent_id, "end", text=item.name, values=[str(item)])
                        
        except Exception as e:
            logger.error(f"Failed to populate tree: {e}")
    
    def _populate_file_list(self):
        """Populate the file list."""
        # Clear existing items
        for item in self.file_list.get_children():
            self.file_list.delete(item)
        
        if not self.current_path or not self.current_path.is_dir():
            return
        
        try:
            items = []
            
            # Add parent directory entry
            if self.current_path.parent != self.current_path:
                items.append(("..", "folder", "", "", ""))
            
            # Add directories and files
            for item in sorted(self.current_path.iterdir()):
                try:
                    if item.is_dir():
                        items.append((item.name, "folder", "", "", "Directory"))
                    else:
                        stat = item.stat()
                        size = self._format_size(stat.st_size)
                        modified = self._format_time(stat.st_mtime)
                        file_type = item.suffix.upper()[1:] if item.suffix else "File"
                        items.append((item.name, "file", size, modified, file_type))
                except (PermissionError, OSError):
                    # Skip files we can't access
                    continue
            
            # Add items to list
            for name, item_type, size, modified, file_type in items:
                icon = "📁" if item_type == "folder" else "📄"
                if item_type == "file" and name.endswith('.py'):
                    icon = "🐍"
                
                self.file_list.insert("", "end", text=icon, 
                                    values=(name, size, modified, file_type))
                
        except Exception as e:
            logger.error(f"Failed to populate file list: {e}")
    
    def _format_size(self, size: int) -> str:
        """Format file size in human-readable format."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def _format_time(self, timestamp: float) -> str:
        """Format timestamp in human-readable format."""
        import datetime
        dt = datetime.datetime.fromtimestamp(timestamp)
        return dt.strftime("%Y-%m-%d %H:%M")
    
    def go_up(self):
        """Navigate to parent directory."""
        if self.current_path and self.current_path.parent != self.current_path:
            self.navigate_to(self.current_path.parent)
    
    def refresh(self):
        """Refresh the current directory."""
        if self.current_path:
            self.navigate_to(self.current_path)
    
    def on_path_enter(self, event):
        """Handle path entry return key."""
        path_str = self.path_var.get().strip()
        if path_str:
            try:
                path = Path(path_str)
                self.navigate_to(path)
            except Exception as e:
                messagebox.showerror("Error", f"Invalid path: {e}")
    
    def on_tree_select(self, event):
        """Handle tree selection."""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item.get('values', [])
            if values:
                path = Path(values[0])
                if path.is_dir():
                    self.navigate_to(path)
    
    def on_tree_double_click(self, event):
        """Handle tree double-click."""
        self.on_tree_select(event)
    
    def on_file_double_click(self, event):
        """Handle file list double-click."""
        selection = self.file_list.selection()
        if selection:
            item = self.file_list.item(selection[0])
            values = item.get('values', [])
            if values:
                name = values[0]
                if name == "..":
                    self.go_up()
                else:
                    path = self.current_path / name
                    if path.is_dir():
                        self.navigate_to(path)
                    elif path.suffix == '.py':
                        self.analyze_selected_file()
    
    def on_file_right_click(self, event):
        """Handle file list right-click."""
        # Select the item under cursor
        item = self.file_list.identify_row(event.y)
        if item:
            self.file_list.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def get_selected_file(self) -> Optional[Path]:
        """Get the currently selected file."""
        selection = self.file_list.selection()
        if selection and self.current_path:
            item = self.file_list.item(selection[0])
            values = item.get('values', [])
            if values and values[0] != "..":
                return self.current_path / values[0]
        return None
    
    def analyze_selected_file(self):
        """Analyze the selected Python file."""
        file_path = self.get_selected_file()
        if file_path and file_path.suffix == '.py':
            self.main_window.analyze_file(file_path)
        else:
            messagebox.showwarning("Warning", "Please select a Python file")
    
    def add_to_bucket(self):
        """Add selected file to a bucket."""
        file_path = self.get_selected_file()
        if not file_path:
            messagebox.showwarning("Warning", "Please select a file")
            return
        
        # Show bucket selection dialog
        buckets = list(self.main_window.config.get_buckets().keys())
        if not buckets:
            messagebox.showinfo("Info", "No buckets available. Create a bucket first.")
            return
        
        # Simple selection dialog (could be enhanced with a proper dialog)
        from tkinter import simpledialog
        bucket_name = simpledialog.askstring("Select Bucket", 
                                            f"Available buckets: {', '.join(buckets)}\n\nEnter bucket name:")
        if bucket_name and bucket_name in buckets:
            self.main_window.add_to_bucket(file_path, bucket_name)
    
    def add_comments(self):
        """Add comments to selected Python file."""
        file_path = self.get_selected_file()
        if file_path and file_path.suffix == '.py':
            self.main_window.add_comments_to_file(file_path)
        else:
            messagebox.showwarning("Warning", "Please select a Python file")
    
    def strip_comments(self):
        """Strip comments from selected Python file."""
        file_path = self.get_selected_file()
        if file_path and file_path.suffix == '.py':
            self.main_window.strip_comments_from_file(file_path)
        else:
            messagebox.showwarning("Warning", "Please select a Python file")
    
    def undo_changes(self):
        """Undo changes to selected file."""
        file_path = self.get_selected_file()
        if file_path:
            self.main_window.undo_last_change(file_path)
        else:
            messagebox.showwarning("Warning", "Please select a file")
    
    def open_in_editor(self):
        """Open selected file in default editor."""
        file_path = self.get_selected_file()
        if file_path:
            try:
                import subprocess
                import sys
                if sys.platform == "win32":
                    os.startfile(file_path)
                elif sys.platform == "darwin":
                    subprocess.run(["open", file_path])
                else:
                    subprocess.run(["xdg-open", file_path])
            except Exception as e:
                messagebox.showerror("Error", f"Failed to open file: {e}")
    
    def show_in_explorer(self):
        """Show selected file in file explorer."""
        file_path = self.get_selected_file()
        if file_path:
            try:
                import subprocess
                import sys
                if sys.platform == "win32":
                    subprocess.run(["explorer", "/select,", str(file_path)])
                elif sys.platform == "darwin":
                    subprocess.run(["open", "-R", str(file_path)])
                else:
                    subprocess.run(["xdg-open", str(file_path.parent)])
            except Exception as e:
                messagebox.showerror("Error", f"Failed to show in explorer: {e}")
    
    def show_analysis(self, analysis: 'FileAnalysis'):
        """Show code analysis results (for right browser)."""
        if self.side != "right":
            return
        
        # Clear current file list
        for item in self.file_list.get_children():
            self.file_list.delete(item)
        
        # Show analysis results
        if analysis.error:
            self.file_list.insert("", "end", text="❌", 
                                values=(f"Error: {analysis.error}", "", "", "Error"))
            return
        
        # Add classes
        for cls in analysis.classes:
            self.file_list.insert("", "end", text="🏛️", 
                                values=(f"Class: {cls.name}", f"Line {cls.line_number}", "", "Class"))
        
        # Add functions
        for func in analysis.functions:
            self.file_list.insert("", "end", text="⚙️", 
                                values=(f"Function: {func.name}", f"Line {func.line_number}", "", "Function"))
        
        # Add methods
        for method in analysis.methods:
            self.file_list.insert("", "end", text="🔧", 
                                values=(f"Method: {method.name}", f"Line {method.line_number}", 
                                       method.parent or "", "Method"))
        
        # Add imports
        for imp in analysis.imports[:10]:  # Limit to first 10 imports
            self.file_list.insert("", "end", text="📦", 
                                values=(f"Import: {imp}", "", "", "Import"))
