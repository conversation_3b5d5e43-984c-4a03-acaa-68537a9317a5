"""
Menu bar for Phoenix application.
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import logging
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .main_window import MainWindow

logger = logging.getLogger(__name__)


class MenuBar:
    """Application menu bar."""
    
    def __init__(self, root: tk.Tk, main_window: 'MainWindow'):
        self.root = root
        self.main_window = main_window
        self.menubar = tk.Menu(root)
        
        self._create_menus()
    
    def _create_menus(self):
        """Create all menu items."""
        # File menu
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="File", menu=file_menu)
        
        file_menu.add_command(label="Open Folder...", command=self.open_folder, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="New Bucket...", command=self.new_bucket, accelerator="Ctrl+N")
        file_menu.add_command(label="Save Project", command=self.save_project, accelerator="Ctrl+S")
        file_menu.add_separator()
        file_menu.add_command(label="Export Bucket...", command=self.export_bucket)
        file_menu.add_command(label="Import Project...", command=self.import_project)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.exit_app, accelerator="Ctrl+Q")
        
        # Edit menu
        edit_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Edit", menu=edit_menu)
        
        edit_menu.add_command(label="Preferences...", command=self.show_preferences)
        edit_menu.add_separator()
        edit_menu.add_command(label="Clear Queue", command=self.clear_queue)
        
        # Tools menu
        tools_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Tools", menu=tools_menu)
        
        tools_menu.add_command(label="Analyze Directory...", command=self.analyze_directory)
        tools_menu.add_separator()
        
        # Comment submenu
        comment_menu = tk.Menu(tools_menu, tearoff=0)
        tools_menu.add_cascade(label="Comments", menu=comment_menu)
        comment_menu.add_command(label="Add Comments to Bucket", command=self.add_comments_to_bucket)
        comment_menu.add_command(label="Strip Comments from Bucket", command=self.strip_comments_from_bucket)
        comment_menu.add_command(label="Undo Changes in Bucket", command=self.undo_changes_in_bucket)
        
        tools_menu.add_separator()
        tools_menu.add_command(label="Cleanup Backups...", command=self.cleanup_backups)
        
        # View menu
        view_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="View", menu=view_menu)
        
        view_menu.add_command(label="Refresh All", command=self.refresh_all, accelerator="F5")
        view_menu.add_separator()
        view_menu.add_command(label="Show Hidden Files", command=self.toggle_hidden_files)
        view_menu.add_command(label="Show File Extensions", command=self.toggle_file_extensions)
        
        # Help menu
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Help", menu=help_menu)
        
        help_menu.add_command(label="About Phoenix", command=self.show_about)
        help_menu.add_command(label="User Guide", command=self.show_user_guide)
        help_menu.add_command(label="Keyboard Shortcuts", command=self.show_shortcuts)
    
    # File menu callbacks
    def open_folder(self):
        """Open folder dialog."""
        self.main_window.open_folder()
    
    def new_bucket(self):
        """Create new bucket."""
        self.main_window.new_bucket()
    
    def save_project(self):
        """Save project."""
        self.main_window.save_project()
    
    def export_bucket(self):
        """Export current bucket."""
        if self.main_window.current_bucket:
            self.main_window.export_bucket(self.main_window.current_bucket)
        else:
            messagebox.showwarning("Warning", "Please select a bucket to export")
    
    def import_project(self):
        """Import project from file."""
        file_path = filedialog.askopenfilename(
            title="Import Project",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            try:
                import json
                from pathlib import Path
                
                with open(file_path, 'r') as f:
                    data = json.load(f)
                
                # Merge with existing project data
                if "buckets" in data:
                    existing_buckets = self.main_window.config.get_buckets()
                    for bucket_name, files in data["buckets"].items():
                        if bucket_name in existing_buckets:
                            # Ask user what to do with existing bucket
                            result = messagebox.askyesnocancel(
                                "Bucket Exists",
                                f"Bucket '{bucket_name}' already exists. "
                                f"Yes = Merge, No = Replace, Cancel = Skip"
                            )
                            if result is True:  # Merge
                                existing_files = set(existing_buckets[bucket_name])
                                new_files = set(files)
                                merged_files = list(existing_files | new_files)
                                self.main_window.config._project_data["buckets"][bucket_name] = merged_files
                            elif result is False:  # Replace
                                self.main_window.config._project_data["buckets"][bucket_name] = files
                            # Cancel = Skip, do nothing
                        else:
                            self.main_window.config._project_data["buckets"][bucket_name] = files
                
                self.main_window.config.save_project_data()
                self.main_window.project_panel.refresh()
                messagebox.showinfo("Success", "Project imported successfully")
                
            except Exception as e:
                logger.error(f"Failed to import project: {e}")
                messagebox.showerror("Error", f"Failed to import project: {e}")
    
    def exit_app(self):
        """Exit application."""
        self.main_window.on_closing()
    
    # Edit menu callbacks
    def show_preferences(self):
        """Show preferences dialog."""
        # TODO: Implement preferences dialog
        messagebox.showinfo("Info", "Preferences dialog not yet implemented")
    
    def clear_queue(self):
        """Clear operation queue."""
        self.main_window.transfer_queue.clear_all()
    
    # Tools menu callbacks
    def analyze_directory(self):
        """Analyze entire directory."""
        directory = filedialog.askdirectory(title="Select Directory to Analyze")
        if directory:
            try:
                from pathlib import Path
                analyses = self.main_window.code_analyzer.analyze_directory(Path(directory))
                summary = self.main_window.code_analyzer.get_summary(analyses)
                
                message = f"Analysis Complete!\n\n"
                message += f"Files analyzed: {summary['total_files']}\n"
                message += f"Classes found: {summary['total_classes']}\n"
                message += f"Functions found: {summary['total_functions']}\n"
                message += f"Methods found: {summary['total_methods']}\n"
                message += f"Total lines: {summary['total_lines']}\n"
                
                if summary['files_with_errors'] > 0:
                    message += f"\nFiles with errors: {summary['files_with_errors']}"
                
                messagebox.showinfo("Analysis Results", message)
                
            except Exception as e:
                logger.error(f"Failed to analyze directory: {e}")
                messagebox.showerror("Error", f"Failed to analyze directory: {e}")
    
    def add_comments_to_bucket(self):
        """Add comments to all files in current bucket."""
        if not self.main_window.current_bucket:
            messagebox.showwarning("Warning", "Please select a bucket")
            return
        
        files = self.main_window.config.get_bucket_files(self.main_window.current_bucket)
        python_files = [f for f in files if f.endswith('.py')]
        
        if not python_files:
            messagebox.showinfo("Info", "No Python files in bucket")
            return
        
        result = messagebox.askyesno("Confirm", 
                                   f"Add comments to {len(python_files)} Python files in bucket "
                                   f"'{self.main_window.current_bucket}'?")
        if result:
            success_count = 0
            for file_path_str in python_files:
                try:
                    from pathlib import Path
                    file_path = Path(file_path_str)
                    if self.main_window.comment_manager.add_comments(file_path):
                        success_count += 1
                        self.main_window.transfer_queue.add_operation("add_comments", file_path)
                except Exception as e:
                    logger.error(f"Failed to add comments to {file_path_str}: {e}")
            
            messagebox.showinfo("Complete", f"Added comments to {success_count}/{len(python_files)} files")
    
    def strip_comments_from_bucket(self):
        """Strip comments from all files in current bucket."""
        if not self.main_window.current_bucket:
            messagebox.showwarning("Warning", "Please select a bucket")
            return
        
        files = self.main_window.config.get_bucket_files(self.main_window.current_bucket)
        python_files = [f for f in files if f.endswith('.py')]
        
        if not python_files:
            messagebox.showinfo("Info", "No Python files in bucket")
            return
        
        result = messagebox.askyesno("Confirm", 
                                   f"Strip comments from {len(python_files)} Python files in bucket "
                                   f"'{self.main_window.current_bucket}'?\n\n"
                                   f"Backups will be created automatically.")
        if result:
            success_count = 0
            for file_path_str in python_files:
                try:
                    from pathlib import Path
                    file_path = Path(file_path_str)
                    if self.main_window.comment_manager.strip_comments(file_path):
                        success_count += 1
                        self.main_window.transfer_queue.add_operation("strip_comments", file_path)
                except Exception as e:
                    logger.error(f"Failed to strip comments from {file_path_str}: {e}")
            
            messagebox.showinfo("Complete", f"Stripped comments from {success_count}/{len(python_files)} files")
    
    def undo_changes_in_bucket(self):
        """Undo changes to all files in current bucket."""
        if not self.main_window.current_bucket:
            messagebox.showwarning("Warning", "Please select a bucket")
            return
        
        files = self.main_window.config.get_bucket_files(self.main_window.current_bucket)
        python_files = [f for f in files if f.endswith('.py')]
        
        if not python_files:
            messagebox.showinfo("Info", "No Python files in bucket")
            return
        
        result = messagebox.askyesno("Confirm", 
                                   f"Undo changes to {len(python_files)} Python files in bucket "
                                   f"'{self.main_window.current_bucket}'?")
        if result:
            success_count = 0
            for file_path_str in python_files:
                try:
                    from pathlib import Path
                    file_path = Path(file_path_str)
                    if self.main_window.comment_manager.undo_last_change(file_path):
                        success_count += 1
                        self.main_window.transfer_queue.add_operation("undo_change", file_path)
                except Exception as e:
                    logger.error(f"Failed to undo changes to {file_path_str}: {e}")
            
            messagebox.showinfo("Complete", f"Undid changes to {success_count}/{len(python_files)} files")
    
    def cleanup_backups(self):
        """Cleanup old backup files."""
        try:
            deleted_count = self.main_window.comment_manager.cleanup_old_backups()
            messagebox.showinfo("Cleanup Complete", f"Deleted {deleted_count} old backup files")
        except Exception as e:
            logger.error(f"Failed to cleanup backups: {e}")
            messagebox.showerror("Error", f"Failed to cleanup backups: {e}")
    
    # View menu callbacks
    def refresh_all(self):
        """Refresh all views."""
        self.main_window.refresh_all()
    
    def toggle_hidden_files(self):
        """Toggle showing hidden files."""
        # TODO: Implement hidden files toggle
        messagebox.showinfo("Info", "Hidden files toggle not yet implemented")
    
    def toggle_file_extensions(self):
        """Toggle showing file extensions."""
        # TODO: Implement file extensions toggle
        messagebox.showinfo("Info", "File extensions toggle not yet implemented")
    
    # Help menu callbacks
    def show_about(self):
        """Show about dialog."""
        about_text = """Phoenix - Python Development Tool
Version 1.0.0

A modern Python development tool rebuilt from the ground up.
Phoenix rises from the ashes with enhanced functionality,
clean architecture, and a FileZilla-inspired interface.

Features:
• File browser with dual-pane layout
• Project bucket management
• Code analysis and visualization
• Comment management with backup/restore
• Operation queue tracking
• Export/import capabilities

Built with Python and tkinter for maximum compatibility."""
        
        messagebox.showinfo("About Phoenix", about_text)
    
    def show_user_guide(self):
        """Show user guide."""
        guide_text = """Phoenix User Guide

Getting Started:
1. Use File > Open Folder to browse your Python projects
2. Create buckets to organize related files
3. Drag files to buckets or use right-click menu
4. Analyze files to see classes, functions, and methods

Key Features:
• Left panel: File browser for navigating directories
• Right panel: Project buckets and code analysis
• Bottom panel: Operations queue showing recent actions

Keyboard Shortcuts:
• Ctrl+O: Open folder
• Ctrl+N: New bucket
• Ctrl+S: Save project
• F5: Refresh all panels
• Delete: Remove selected items

Tips:
• Right-click files for context menu options
• Double-click Python files to analyze them
• Use the operations queue to track your work
• Backups are created automatically when modifying files"""
        
        messagebox.showinfo("User Guide", guide_text)
    
    def show_shortcuts(self):
        """Show keyboard shortcuts."""
        shortcuts_text = """Keyboard Shortcuts

File Operations:
Ctrl+O          Open folder
Ctrl+N          New bucket
Ctrl+S          Save project
Ctrl+Q          Exit application

View Operations:
F5              Refresh all panels
Delete          Remove selected item

Navigation:
Double-click    Open folder or analyze file
Right-click     Show context menu
Enter           Activate selected item

File Browser:
↑ Button        Go to parent directory
⟳ Button        Refresh current directory

Project Panel:
Delete          Remove from bucket
Double-click    Analyze file"""
        
        messagebox.showinfo("Keyboard Shortcuts", shortcuts_text)
