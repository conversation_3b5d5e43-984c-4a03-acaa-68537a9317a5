"""
Configuration management for Phoenix application.
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class Config:
    """Manages application configuration and persistence."""
    
    def __init__(self, config_dir: Optional[Path] = None):
        """Initialize configuration manager.
        
        Args:
            config_dir: Directory to store config files. Defaults to current directory.
        """
        self.config_dir = config_dir or Path.cwd()
        self.config_dir.mkdir(exist_ok=True)
        
        self.project_db_path = self.config_dir / "phoenix_projects.json"
        self.settings_path = self.config_dir / "phoenix_settings.json"
        
        self._project_data: Dict[str, Any] = {}
        self._settings: Dict[str, Any] = {}
        
        self.load_all()
    
    def load_all(self) -> None:
        """Load all configuration files."""
        self.load_project_data()
        self.load_settings()
    
    def load_project_data(self) -> None:
        """Load project database."""
        try:
            if self.project_db_path.exists():
                with open(self.project_db_path, 'r', encoding='utf-8') as f:
                    self._project_data = json.load(f)
            else:
                self._project_data = {"buckets": {}, "recent_folders": []}
                self.save_project_data()
        except Exception as e:
            logger.error(f"Failed to load project data: {e}")
            self._project_data = {"buckets": {}, "recent_folders": []}
    
    def load_settings(self) -> None:
        """Load application settings."""
        try:
            if self.settings_path.exists():
                with open(self.settings_path, 'r', encoding='utf-8') as f:
                    self._settings = json.load(f)
            else:
                self._settings = {
                    "theme": "dark",
                    "window_geometry": "1400x900",
                    "dev_mode": True,
                    "auto_backup": True,
                    "familiar_mode": "phoenix"
                }
                self.save_settings()
        except Exception as e:
            logger.error(f"Failed to load settings: {e}")
            self._settings = {}
    
    def save_project_data(self) -> None:
        """Save project database."""
        try:
            with open(self.project_db_path, 'w', encoding='utf-8') as f:
                json.dump(self._project_data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save project data: {e}")
    
    def save_settings(self) -> None:
        """Save application settings."""
        try:
            with open(self.settings_path, 'w', encoding='utf-8') as f:
                json.dump(self._settings, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save settings: {e}")
    
    @property
    def project_data(self) -> Dict[str, Any]:
        """Get project data."""
        return self._project_data
    
    @property
    def settings(self) -> Dict[str, Any]:
        """Get settings."""
        return self._settings
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a specific setting value."""
        return self._settings.get(key, default)
    
    def set_setting(self, key: str, value: Any) -> None:
        """Set a specific setting value."""
        self._settings[key] = value
        self.save_settings()
    
    def get_buckets(self) -> Dict[str, list]:
        """Get all project buckets."""
        return self._project_data.get("buckets", {})
    
    def add_bucket(self, name: str) -> bool:
        """Add a new bucket.
        
        Args:
            name: Bucket name
            
        Returns:
            True if bucket was added, False if it already exists
        """
        if name in self._project_data.get("buckets", {}):
            return False
        
        if "buckets" not in self._project_data:
            self._project_data["buckets"] = {}
        
        self._project_data["buckets"][name] = []
        self.save_project_data()
        return True
    
    def remove_bucket(self, name: str) -> bool:
        """Remove a bucket.
        
        Args:
            name: Bucket name
            
        Returns:
            True if bucket was removed, False if it didn't exist
        """
        buckets = self._project_data.get("buckets", {})
        if name in buckets:
            del buckets[name]
            self.save_project_data()
            return True
        return False
    
    def add_file_to_bucket(self, bucket_name: str, file_path: str) -> bool:
        """Add a file to a bucket.
        
        Args:
            bucket_name: Name of the bucket
            file_path: Path to the file
            
        Returns:
            True if file was added, False if bucket doesn't exist or file already in bucket
        """
        buckets = self._project_data.get("buckets", {})
        if bucket_name not in buckets:
            return False
        
        if file_path not in buckets[bucket_name]:
            buckets[bucket_name].append(file_path)
            self.save_project_data()
            return True
        return False
    
    def remove_file_from_bucket(self, bucket_name: str, file_path: str) -> bool:
        """Remove a file from a bucket.
        
        Args:
            bucket_name: Name of the bucket
            file_path: Path to the file
            
        Returns:
            True if file was removed, False if bucket doesn't exist or file not in bucket
        """
        buckets = self._project_data.get("buckets", {})
        if bucket_name not in buckets:
            return False
        
        if file_path in buckets[bucket_name]:
            buckets[bucket_name].remove(file_path)
            self.save_project_data()
            return True
        return False
    
    def get_bucket_files(self, bucket_name: str) -> list:
        """Get all files in a bucket.
        
        Args:
            bucket_name: Name of the bucket
            
        Returns:
            List of file paths in the bucket
        """
        return self._project_data.get("buckets", {}).get(bucket_name, [])
