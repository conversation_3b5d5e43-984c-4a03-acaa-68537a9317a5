"""
Main window for Phoenix application with FileZilla-style interface.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
from pathlib import Path
from typing import Optional, Dict, Any

from .file_browser import FileBrowser
from .project_panel import ProjectPanel
from .transfer_queue import TransferQueue
from .menu_bar import MenuBar
from .toolbar import Toolbar
from .status_bar import StatusBar
from ..core.project_manager import ProjectManager
from ..core.code_analyzer import CodeAnalyzer
from ..core.comment_manager import CommentManager
from ..utils.config import Config

logger = logging.getLogger(__name__)


class MainWindow:
    """Main application window with FileZilla-style layout."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Phoenix - Python Development Tool")
        self.root.geometry("1400x900")
        self.root.minsize(800, 600)
        
        # Initialize core components
        self.config = Config()
        self.project_manager = ProjectManager(self.config)
        self.code_analyzer = CodeAnalyzer()
        self.comment_manager = CommentManager()
        
        # UI Components
        self.menu_bar: Optional[MenuBar] = None
        self.toolbar: Optional[Toolbar] = None
        self.status_bar: Optional[StatusBar] = None
        self.left_browser: Optional[FileBrowser] = None
        self.right_browser: Optional[FileBrowser] = None
        self.project_panel: Optional[ProjectPanel] = None
        self.transfer_queue: Optional[TransferQueue] = None
        
        # State
        self.current_left_path: Optional[Path] = None
        self.current_right_path: Optional[Path] = None
        
        self._setup_ui()
        self._setup_bindings()
        self._load_settings()
    
    def _setup_ui(self):
        """Set up the user interface."""
        # Menu bar
        self.menu_bar = MenuBar(self.root, self)
        self.root.config(menu=self.menu_bar.menubar)
        
        # Toolbar
        self.toolbar = Toolbar(self.root, self)
        self.toolbar.pack(fill=tk.X, padx=2, pady=2)
        
        # Main content area with paned windows (FileZilla style)
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Left panel - File browser
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=2)
        
        # Left browser
        ttk.Label(left_frame, text="Local Files", font=("Arial", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
        self.left_browser = FileBrowser(left_frame, self, side="left")
        self.left_browser.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Right panel - split between project panel and right browser
        right_paned = ttk.PanedWindow(main_paned, orient=tk.VERTICAL)
        main_paned.add(right_paned, weight=3)
        
        # Project panel (top right)
        project_frame = ttk.Frame(right_paned)
        right_paned.add(project_frame, weight=1)
        
        ttk.Label(project_frame, text="Project Buckets", font=("Arial", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
        self.project_panel = ProjectPanel(project_frame, self)
        self.project_panel.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Right browser (bottom right)
        browser_frame = ttk.Frame(right_paned)
        right_paned.add(browser_frame, weight=2)
        
        ttk.Label(browser_frame, text="Code Analysis", font=("Arial", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
        self.right_browser = FileBrowser(browser_frame, self, side="right")
        self.right_browser.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Bottom panel - Transfer queue
        bottom_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        bottom_paned.pack(fill=tk.X, padx=2, pady=2)
        
        transfer_frame = ttk.Frame(bottom_paned)
        bottom_paned.add(transfer_frame, weight=1)
        
        ttk.Label(transfer_frame, text="Operations Queue", font=("Arial", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
        self.transfer_queue = TransferQueue(transfer_frame, self)
        self.transfer_queue.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Status bar
        self.status_bar = StatusBar(self.root, self)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
    
    def _setup_bindings(self):
        """Set up keyboard and window bindings."""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Keyboard shortcuts
        self.root.bind("<Control-o>", lambda e: self.open_folder())
        self.root.bind("<Control-n>", lambda e: self.new_bucket())
        self.root.bind("<Control-s>", lambda e: self.save_project())
        self.root.bind("<Control-q>", lambda e: self.on_closing())
        self.root.bind("<F5>", lambda e: self.refresh_all())
    
    def _load_settings(self):
        """Load application settings."""
        try:
            geometry = self.config.get_setting("window_geometry", "1400x900")
            self.root.geometry(geometry)
            
            # Load last opened directories
            last_left = self.config.get_setting("last_left_directory")
            last_right = self.config.get_setting("last_right_directory")
            
            if last_left and Path(last_left).exists():
                self.left_browser.navigate_to(Path(last_left))
            
            if last_right and Path(last_right).exists():
                self.right_browser.navigate_to(Path(last_right))
                
        except Exception as e:
            logger.error(f"Failed to load settings: {e}")
    
    def _save_settings(self):
        """Save application settings."""
        try:
            # Save window geometry
            self.config.set_setting("window_geometry", self.root.geometry())
            
            # Save current directories
            if self.current_left_path:
                self.config.set_setting("last_left_directory", str(self.current_left_path))
            if self.current_right_path:
                self.config.set_setting("last_right_directory", str(self.current_right_path))
                
        except Exception as e:
            logger.error(f"Failed to save settings: {e}")
    
    def run(self):
        """Start the application."""
        try:
            self.root.mainloop()
        except Exception as e:
            logger.error(f"Application error: {e}")
            messagebox.showerror("Error", f"Application error: {e}")
    
    def on_closing(self):
        """Handle application closing."""
        try:
            self._save_settings()
            self.root.destroy()
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
            self.root.destroy()
    
    # Menu and toolbar callbacks
    def open_folder(self):
        """Open a folder in the left browser."""
        folder = filedialog.askdirectory(title="Select Folder")
        if folder:
            self.left_browser.navigate_to(Path(folder))
    
    def new_bucket(self):
        """Create a new project bucket."""
        self.project_panel.create_new_bucket()
    
    def save_project(self):
        """Save the current project."""
        self.project_manager.save_project()
        self.status_bar.set_message("Project saved")
    
    def refresh_all(self):
        """Refresh all panels."""
        if self.left_browser:
            self.left_browser.refresh()
        if self.right_browser:
            self.right_browser.refresh()
        if self.project_panel:
            self.project_panel.refresh()
        self.status_bar.set_message("Refreshed")
    
    def analyze_file(self, file_path: Path):
        """Analyze a Python file and show results."""
        try:
            analysis = self.code_analyzer.analyze_file(file_path)
            self.right_browser.show_analysis(analysis)
            self.status_bar.set_message(f"Analyzed {file_path.name}")
        except Exception as e:
            logger.error(f"Failed to analyze file {file_path}: {e}")
            messagebox.showerror("Analysis Error", f"Failed to analyze file: {e}")
    
    def add_to_bucket(self, file_path: Path, bucket_name: str):
        """Add a file to a project bucket."""
        try:
            success = self.project_manager.add_file_to_bucket(bucket_name, file_path)
            if success:
                self.project_panel.refresh()
                self.transfer_queue.add_operation("add_to_bucket", file_path, bucket_name)
                self.status_bar.set_message(f"Added {file_path.name} to {bucket_name}")
            else:
                messagebox.showwarning("Warning", f"File already in bucket or bucket doesn't exist")
        except Exception as e:
            logger.error(f"Failed to add file to bucket: {e}")
            messagebox.showerror("Error", f"Failed to add file to bucket: {e}")
    
    def remove_from_bucket(self, file_path: Path, bucket_name: str):
        """Remove a file from a project bucket."""
        try:
            success = self.project_manager.remove_file_from_bucket(bucket_name, file_path)
            if success:
                self.project_panel.refresh()
                self.transfer_queue.add_operation("remove_from_bucket", file_path, bucket_name)
                self.status_bar.set_message(f"Removed {file_path.name} from {bucket_name}")
            else:
                messagebox.showwarning("Warning", f"File not in bucket or bucket doesn't exist")
        except Exception as e:
            logger.error(f"Failed to remove file from bucket: {e}")
            messagebox.showerror("Error", f"Failed to remove file from bucket: {e}")
    
    def export_bucket(self, bucket_name: str):
        """Export a bucket to ZIP file."""
        try:
            files = self.project_manager.get_bucket_files(bucket_name)
            if not files:
                messagebox.showinfo("Info", "Bucket is empty")
                return
            
            output_file = filedialog.asksaveasfilename(
                title=f"Export {bucket_name}",
                defaultextension=".zip",
                filetypes=[("ZIP files", "*.zip")]
            )
            
            if output_file:
                success = self.project_manager.export_bucket_to_zip(bucket_name, Path(output_file))
                if success:
                    self.transfer_queue.add_operation("export_bucket", bucket_name, output_file)
                    self.status_bar.set_message(f"Exported {bucket_name} to {output_file}")
                    messagebox.showinfo("Success", f"Bucket exported to {output_file}")
                else:
                    messagebox.showerror("Error", "Failed to export bucket")
        except Exception as e:
            logger.error(f"Failed to export bucket: {e}")
            messagebox.showerror("Error", f"Failed to export bucket: {e}")
    
    def add_comments_to_file(self, file_path: Path):
        """Add comments to a Python file."""
        try:
            success = self.comment_manager.add_comments(file_path)
            if success:
                self.transfer_queue.add_operation("add_comments", file_path)
                self.status_bar.set_message(f"Added comments to {file_path.name}")
                messagebox.showinfo("Success", f"Comments added to {file_path.name}")
            else:
                messagebox.showerror("Error", "Failed to add comments")
        except Exception as e:
            logger.error(f"Failed to add comments: {e}")
            messagebox.showerror("Error", f"Failed to add comments: {e}")
    
    def strip_comments_from_file(self, file_path: Path):
        """Strip comments from a Python file."""
        try:
            success = self.comment_manager.strip_comments(file_path)
            if success:
                self.transfer_queue.add_operation("strip_comments", file_path)
                self.status_bar.set_message(f"Stripped comments from {file_path.name}")
                messagebox.showinfo("Success", f"Comments stripped from {file_path.name}")
            else:
                messagebox.showerror("Error", "Failed to strip comments")
        except Exception as e:
            logger.error(f"Failed to strip comments: {e}")
            messagebox.showerror("Error", f"Failed to strip comments: {e}")
    
    def undo_last_change(self, file_path: Path):
        """Undo the last change to a file."""
        try:
            success = self.comment_manager.undo_last_change(file_path)
            if success:
                self.transfer_queue.add_operation("undo_change", file_path)
                self.status_bar.set_message(f"Undid changes to {file_path.name}")
                messagebox.showinfo("Success", f"Changes undone for {file_path.name}")
            else:
                messagebox.showwarning("Warning", "No backup found to restore from")
        except Exception as e:
            logger.error(f"Failed to undo changes: {e}")
            messagebox.showerror("Error", f"Failed to undo changes: {e}")
    
    # Properties for UI components to access
    @property
    def current_bucket(self) -> Optional[str]:
        """Get the currently selected bucket."""
        return self.project_panel.current_bucket if self.project_panel else None
