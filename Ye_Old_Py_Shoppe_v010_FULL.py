"""
Ye Old Py Shoppe v0.10 – Animations, Familiars & Dev Mode
All features wired: buckets, diff, spellbook, auto-whisper, animations, familiars, Dev Mode.
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog
import os, sys, subprocess, threading, zipfile, json, ast, difflib, time
from pathlib import Path

# Attempt OpenAI import
try:
    import openai
except ImportError:
    openai = None

# Config files
CONFIG_FILE    = "user_config.json"
PROJECT_DB     = "project_db.json"
INTEL_DB       = "intelligence_db.json"
DEV_MODE_FILE  = "dev_mode.json"
BATCH_SIZE     = 25
ANIM_INTERVAL  = 500  # ms
PYTHON_VERSIONS = [sys.executable]

class YeOldPyShoppeApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("Ye Old Py Shoppe 🪄 v0.10")
        self.geometry("1400x900")
        self.resizable(False, False)

        # Load state
        self.db = self._load_json(PROJECT_DB, {"buckets": {}})
        self.intel = self._load_json(INTEL_DB, {"hits": {}})
        self.dev_mode = self._load_json(DEV_MODE_FILE, {"enabled": False}).get("enabled", False)
        self.api_key = ""
        self._load_config()

        # Runtime
        self.results = []
        self.batch_index = 0
        self.whisper_queue = []
        self.spell_query = ""
        self.current_bucket = None

        # Build UI
        self._build_ui()

        # Background threads
        threading.Thread(target=self._auto_whisper_loop, daemon=True).start()
        threading.Thread(target=self._pulse_icons_loop, daemon=True).start()

    def _load_json(self, path, default):
        try:
            return json.loads(Path(path).read_text())
        except:
            return default

    def _save_json(self, path, data):
        Path(path).write_text(json.dumps(data, indent=2))

    def _load_config(self):
        cfg = self._load_json(CONFIG_FILE, {})
        self.api_key = cfg.get("api_key", "")
        if openai and self.api_key:
            openai.api_key = self.api_key

    def _save_config(self):
        self._save_json(CONFIG_FILE, {"api_key": self.api_key})

    def _toggle_dev_mode(self):
        if not self.dev_mode:
            pwd = simpledialog.askstring("Dev Mode", "Enter password:", show="*")
            if pwd != "letmein":
                messagebox.showwarning("Dev Mode", "Incorrect password.")
                return
        self.dev_mode = not self.dev_mode
        self._save_json(DEV_MODE_FILE, {"enabled": self.dev_mode})
        if self.dev_mode:
            self.dev_switch.select()
        else:
            self.dev_switch.deselect()
        messagebox.showinfo("Dev Mode", f"{'Enabled' if self.dev_mode else 'Disabled'}")

    def _toggle_ai(self):
        if self.ai_toggle.get():
            if openai is None:
                if messagebox.askyesno("Install openai","Install OpenAI SDK?"):
                    subprocess.check_call([sys.executable,"-m","pip","install","openai"])
                    import importlib; importlib.reload(sys.modules.get('openai'))
            if not self.api_key:
                key = simpledialog.askstring("API Key","Enter API key:",show="*")
                if key:
                    self.api_key=key.strip()
                    if openai: openai.api_key=self.api_key
                    self._save_config()
        messagebox.showinfo("AI Advisor", "On" if self.ai_toggle.get() else "Off")

    def _pulse_icons_loop(self):
        toggle = False
        while True:
            for icon in (self.icon_eye, self.icon_brain, self.icon_paw):
                icon.configure(text_color="white" if toggle else "#888")
            toggle = not toggle
            time.sleep(ANIM_INTERVAL/1000)

    def _build_ui(self):
        # Top bar
        top = ctk.CTkFrame(self)
        top.pack(fill="x",padx=5,pady=5)
        self.dev_switch = ctk.CTkSwitch(top,text="Dev Mode",command=self._toggle_dev_mode)
        if self.dev_mode:
            self.dev_switch.select()
        self.dev_switch.pack(side="left",padx=10)
        ctk.CTkLabel(top,text="Familiar:").pack(side="left",padx=5)
        self.fam_var=ctk.StringVar(value="Speccles")
        ctk.CTkOptionMenu(top,values=["Speccles","Pazuzu","Grimm","LeVay","Nyx","Owl","Fox","Serpent"],variable=self.fam_var).pack(side="left",padx=5)
        self.icon_eye=ctk.CTkLabel(top,text="👁️")
        self.icon_brain=ctk.CTkLabel(top,text="🧠")
        self.icon_paw=ctk.CTkLabel(top,text="🐾")
        for ic in (self.icon_eye,self.icon_brain,self.icon_paw):
            ic.pack(side="left",padx=2)
        self.spell_var=ctk.StringVar()
        ctk.CTkEntry(top,placeholder_text="Spellbook...",textvariable=self.spell_var,width=300).pack(side="left",padx=10)
        ctk.CTkButton(top,text="🔮",command=self._semantic_search).pack(side="left")
        self.ai_toggle=ctk.CTkSwitch(top,text="AI Advisor",command=self._toggle_ai)
        self.ai_toggle.pack(side="left",padx=10)
        ctk.CTkButton(top,text="📦 Export Bucket",command=self._export_bucket).pack(side="left",padx=5)
        self.py_run_var=ctk.StringVar(value=PYTHON_VERSIONS[0])
        ctk.CTkOptionMenu(top,values=PYTHON_VERSIONS,variable=self.py_run_var).pack(side="left",padx=5)
        ctk.CTkButton(top,text="Run",command=self._run_python).pack(side="left",padx=5)
        # Sidebar
        self.sidebar=ctk.CTkScrollableFrame(self,width=200)
        self.sidebar.pack(side="left",fill="y",padx=(5,0),pady=5)
        ctk.CTkLabel(self.sidebar,text="Buckets",font=("Arial",16)).pack(pady=5)
        ctk.CTkButton(self.sidebar,text="+ New Bucket",command=self._new_bucket).pack(pady=5)
        self.export_btn=ctk.CTkButton(self.sidebar,text="📦 Export",command=self._export_bucket,state="disabled")
        self.export_btn.pack(pady=5)
        self.bucket_buttons={}
        self._refresh_buckets()
        # Main
        self.main_frame=ctk.CTkFrame(self)
        self.main_frame.pack(side="left",fill="both",expand=True,padx=5,pady=5)
        self.results_frame=ctk.CTkScrollableFrame(self.main_frame,width=1000,height=700)
        self.results_frame.pack(fill="both",expand=True)
        self.load_more_btn=ctk.CTkButton(self.main_frame,text="Load More",command=self._load_more,state="disabled")
        self.load_more_btn.pack(pady=5)

    # Bucket methods
    def _new_bucket(self):
        name=simpledialog.askstring("Bucket","Name:")
        if not name: return
        self.db.setdefault("buckets",{})[name]=[]
        self._save_json(PROJECT_DB,self.db)
        self._refresh_buckets()

    def _refresh_buckets(self):
        for btn in self.bucket_buttons.values(): btn.destroy()
        self.bucket_buttons.clear()
        for name in self.db.get("buckets",{}):
            btn=ctk.CTkButton(self.sidebar,text=name,command=lambda n=name:self._select_bucket(n))
            btn.pack(fill="x",padx=5,pady=2)
            self.bucket_buttons[name]=btn

    def _select_bucket(self,name):
        self.current_bucket=name
        self.export_btn.configure(state="normal")
        for w in self.results_frame.winfo_children():
            w.destroy()
        for p in self.db["buckets"][name]:
            self._show_file(p)
        self.load_more_btn.configure(state="disabled")

    def _add_to_bucket(self,path):
        if not self.current_bucket:
            messagebox.showwarning("No Bucket","Select first")
            return
        bl=self.db["buckets"].setdefault(self.current_bucket,[])
        if path in bl:
            messagebox.showinfo("Exists","Already")
            return
        bl.append(path)
        self._save_json(PROJECT_DB,self.db)
        messagebox.showinfo("Added",path)

    # Scan and display
    def _scan_folder(self):
        folder=filedialog.askdirectory()
        if not folder: return
        self.results=[];self.batch_index=0
        for w in self.results_frame.winfo_children(): w.destroy()
        for r,d,f in os.walk(folder):
            for fn in f:
                fp=os.path.join(r,fn)
                if fn.endswith(".py"):
                    self.results.append(fp)
                elif fn.lower().endswith(".zip"):
                    try:
                        with zipfile.ZipFile(fp) as zf:
                            for info in zf.infolist():
                                if info.filename.endswith(".py"):
                                    self.results.append(f"archive://{fp}!{info.filename}")
                    except: pass
        self._load_more()

    def _load_more(self):
        end=self.batch_index+BATCH_SIZE
        batch=self.results[self.batch_index:end]
        for p in batch: self._show_file(p)
        self.batch_index=end
        self.load_more_btn.configure(state="normal" if self.batch_index<len(self.results) else "disabled")

    def _show_file(self,path):
        fr=ctk.CTkFrame(self.results_frame,corner_radius=5)
        fr.pack(fill="x",pady=2,padx=5)
        text_color="black" if self.dev_mode else "gray"
        ctk.CTkLabel(fr,text=path,anchor="w",text_color=text_color).pack(side="left",fill="x",expand=True)
        actions=[
            ("🧺",lambda p=path:self._add_to_bucket(p),"Add to Bucket"),
            ("🔍",lambda p=path:self._extract(p),"Inspect Classes"),
            ("VS",lambda p=path:self._open_vscode(p),"Open in VS Code"),
            ("CMD",lambda p=path:self._open_cmd(p),"Open CMD Here"),
            ("PS",lambda p=path:self._open_ps(p),"Open PowerShell Here"),
            ("⚡",lambda p=path:self._open_admin(p),"Open Elevated CMD"),
        ]
        for txt,cmd,tip in actions:
            btn=ctk.CTkButton(fr,text=txt,width=30,command=cmd)
            btn.pack(side="right",padx=2)
            btn.bind("<Enter>",lambda e,t=tip:self._show_tooltip(e.widget,t))
            btn.bind("<Leave>",lambda e:self._hide_tooltip())

    def _show_tooltip(self,widget,text):
        x,y,_,_=widget.bbox("insert")
        x+=widget.winfo_rootx()+20; y+=widget.winfo_rooty()+20
        self.tooltip=tk.Toplevel(widget);self.tooltip.wm_overrideredirect(True);self.tooltip.wm_geometry(f"+{x}+{y}")
        lbl=tk.Label(self.tooltip,text=text,background="#ffffe0",relief="solid",borderwidth=1,font=("Segoe UI",8))
        lbl.pack(ipadx=4)

    def _hide_tooltip(self):
        if hasattr(self,"tooltip"): self.tooltip.destroy();del self.tooltip

    # Class extraction
    def _extract(self,path):
        try:
            if path.startswith("archive://"):
                zp,fn=path[len("archive://"):].split("!")
                code=zipfile.ZipFile(zp).read(fn).decode("utf-8",errors="ignore")
            else:
                code=Path(path).read_text(errors="ignore")
            tree=ast.parse(code);classes=[c for c in tree.body if isinstance(c,ast.ClassDef)]
            if not classes: messagebox.showinfo("No Classes","None");return
            names=[c.name for c in classes];sel=simpledialog.askstring("Select Class",",".join(names))
            for c in classes:
                if c.name==sel:
                    lines=code.splitlines()[c.lineno-1:c.end_lineno];out="\n".join(lines)
                    Path(f"extracted_{sel}.txt").write_text(out);messagebox.showinfo("Saved",f"extracted_{sel}.txt")
                    return
        except Exception as e:
            messagebox.showerror("Error",str(e))

    # Semantic search stub
    def _semantic_search(self):
        q=self.spell_var.get().strip()
        if not q: return
        self.results=[p for p in self.results if q.lower() in (Path(p).read_text(errors="ignore") if not p.startswith("archive://") else "")]
        self.batch_index=0
        for w in self.results_frame.winfo_children(): w.destroy()
        self._load_more()

    # Auto-whisper
    def _auto_whisper_loop(self):
        while True:
            if self.ai_toggle.get() and openai and self.api_key and self.whisper_queue:
                sn=self.whisper_queue.pop(0)
                try:
                    resp=openai.ChatCompletion.create(model="gpt-3.5-turbo",messages=[{"role":"system","content":sn.get("code","")}])
                    # inline display stub
                except: pass
            time.sleep(10)

    # Export bucket
    def _export_bucket(self):
        name=simpledialog.askstring("Export Bucket","Enter bucket name:")
        if not name: return
        files=self.db.get("buckets",{}).get(name,[])
        if not files: messagebox.showwarning("Empty","No files");return
        tgt=filedialog.asksaveasfilename(defaultextension=".zip",initialfile=f"{name}.zip")
        if not tgt: return
        with zipfile.ZipFile(tgt,"w") as zf:
            for p in files:
                if p.startswith("archive://"):
                    zp,mem=p[len("archive://"):].split("!")
                    data=zipfile.ZipFile(zp).read(mem);zf.writestr(Path(mem).name,data)
                else:
                    zf.write(p,arcname=Path(p).name)
        messagebox.showinfo("Exported",tgt)

    # Run Python
    def _run_python(self):
        ver=self.py_run_var.get();messagebox.showinfo("Run",f"Would run with {ver}")

    # Shell commands
    def _open_vscode(self,path):
        if "!" not in path: subprocess.Popen(["code","-g",f"{path}:1"])

    def _open_cmd(self,path):
        d=os.path.dirname(path)
        if os.name=="nt": subprocess.Popen(["cmd","/K"],cwd=d)
        else: subprocess.Popen(["xdg-open",d])

    def _open_ps(self,path):
        if os.name=="nt": subprocess.Popen(["powershell"],cwd=os.path.dirname(path))
        else: messagebox.showinfo("PS","Windows only")

    def _open_admin(self,path):
        if os.name=="nt": subprocess.Popen(["powershell","Start-Process","cmd","-Verb","runAs"],cwd=os.path.dirname(path))
        else: messagebox.showinfo("Admin","Windows only")

if __name__=="__main__":
    app=YeOldPyShoppeApp()
    app.mainloop()
