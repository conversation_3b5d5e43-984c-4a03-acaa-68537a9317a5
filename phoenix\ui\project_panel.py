"""
Project panel for managing buckets and files.
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import logging
from pathlib import Path
from typing import Optional, List, TYPE_CHECKING

if TYPE_CHECKING:
    from .main_window import MainWindow

logger = logging.getLogger(__name__)


class ProjectPanel(ttk.Frame):
    """Panel for managing project buckets and their contents."""
    
    def __init__(self, parent, main_window: 'MainWindow'):
        super().__init__(parent)
        self.main_window = main_window
        self.current_bucket: Optional[str] = None
        
        self._setup_ui()
        self._setup_bindings()
        self.refresh()
    
    def _setup_ui(self):
        """Set up the project panel UI."""
        # Toolbar
        toolbar = ttk.Frame(self)
        toolbar.pack(fill=tk.X, padx=2, pady=2)
        
        ttk.Button(toolbar, text="New Bucket", command=self.create_new_bucket).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Delete Bucket", command=self.delete_bucket).pack(side=tk.LEFT, padx=2)
        ttk.But<PERSON>(toolbar, text="Export Bucket", command=self.export_bucket).pack(side=tk.LEFT, padx=2)
        
        # Main content with paned window
        paned = ttk.PanedWindow(self, orient=tk.VERTICAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Bucket list (top)
        bucket_frame = ttk.Frame(paned)
        paned.add(bucket_frame, weight=1)
        
        ttk.Label(bucket_frame, text="Buckets").pack(anchor=tk.W)
        
        # Bucket listbox
        bucket_list_frame = ttk.Frame(bucket_frame)
        bucket_list_frame.pack(fill=tk.BOTH, expand=True)
        
        self.bucket_listbox = tk.Listbox(bucket_list_frame, selectmode=tk.SINGLE)
        bucket_scroll = ttk.Scrollbar(bucket_list_frame, orient=tk.VERTICAL, command=self.bucket_listbox.yview)
        self.bucket_listbox.configure(yscrollcommand=bucket_scroll.set)
        
        self.bucket_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        bucket_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # File list for selected bucket (bottom)
        file_frame = ttk.Frame(paned)
        paned.add(file_frame, weight=2)
        
        ttk.Label(file_frame, text="Files in Bucket").pack(anchor=tk.W)
        
        # File treeview with details
        columns = ("path", "size", "modified")
        self.file_tree = ttk.Treeview(file_frame, columns=columns, show="tree headings")
        
        # Configure columns
        self.file_tree.heading("#0", text="Name")
        self.file_tree.column("#0", width=200, minwidth=100)
        self.file_tree.heading("path", text="Path")
        self.file_tree.column("path", width=300, minwidth=150)
        self.file_tree.heading("size", text="Size")
        self.file_tree.column("size", width=80, minwidth=50)
        self.file_tree.heading("modified", text="Modified")
        self.file_tree.column("modified", width=120, minwidth=80)
        
        # Scrollbars for file tree
        file_scroll_y = ttk.Scrollbar(file_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        file_scroll_x = ttk.Scrollbar(file_frame, orient=tk.HORIZONTAL, command=self.file_tree.xview)
        self.file_tree.configure(yscrollcommand=file_scroll_y.set, xscrollcommand=file_scroll_x.set)
        
        self.file_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        file_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        file_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Context menu for files
        self.file_context_menu = tk.Menu(self, tearoff=0)
        self.file_context_menu.add_command(label="Remove from Bucket", command=self.remove_selected_file)
        self.file_context_menu.add_command(label="Analyze File", command=self.analyze_selected_file)
        self.file_context_menu.add_separator()
        self.file_context_menu.add_command(label="Open in Editor", command=self.open_selected_file)
        self.file_context_menu.add_command(label="Show in Explorer", command=self.show_selected_file)
    
    def _setup_bindings(self):
        """Set up event bindings."""
        # Bucket selection
        self.bucket_listbox.bind("<<ListboxSelect>>", self.on_bucket_select)
        self.bucket_listbox.bind("<Double-1>", self.on_bucket_double_click)
        
        # File tree events
        self.file_tree.bind("<Double-1>", self.on_file_double_click)
        self.file_tree.bind("<Button-3>", self.on_file_right_click)
        
        # Keyboard shortcuts
        self.bucket_listbox.bind("<Delete>", lambda e: self.delete_bucket())
        self.file_tree.bind("<Delete>", lambda e: self.remove_selected_file())
    
    def refresh(self):
        """Refresh the bucket list and file list."""
        self._populate_bucket_list()
        self._populate_file_list()
    
    def _populate_bucket_list(self):
        """Populate the bucket listbox."""
        # Save current selection
        current_selection = None
        if self.bucket_listbox.curselection():
            current_selection = self.bucket_listbox.get(self.bucket_listbox.curselection()[0])
        
        # Clear and repopulate
        self.bucket_listbox.delete(0, tk.END)
        
        buckets = self.main_window.config.get_buckets()
        for bucket_name in sorted(buckets.keys()):
            file_count = len(buckets[bucket_name])
            display_text = f"{bucket_name} ({file_count} files)"
            self.bucket_listbox.insert(tk.END, display_text)
        
        # Restore selection
        if current_selection:
            for i in range(self.bucket_listbox.size()):
                if self.bucket_listbox.get(i).startswith(current_selection.split(' (')[0]):
                    self.bucket_listbox.selection_set(i)
                    break
    
    def _populate_file_list(self):
        """Populate the file list for the selected bucket."""
        # Clear existing items
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)
        
        if not self.current_bucket:
            return
        
        files = self.main_window.config.get_bucket_files(self.current_bucket)
        
        for file_path_str in files:
            try:
                file_path = Path(file_path_str)
                
                # Get file info
                if file_path.exists():
                    stat = file_path.stat()
                    size = self._format_size(stat.st_size)
                    modified = self._format_time(stat.st_mtime)
                    icon = "🐍" if file_path.suffix == '.py' else "📄"
                    status = ""
                else:
                    size = "N/A"
                    modified = "N/A"
                    icon = "❌"
                    status = " (missing)"
                
                # Insert into tree
                self.file_tree.insert("", "end", text=f"{icon} {file_path.name}{status}",
                                    values=(str(file_path), size, modified))
                
            except Exception as e:
                logger.error(f"Error processing file {file_path_str}: {e}")
                self.file_tree.insert("", "end", text=f"❌ {file_path_str}",
                                    values=("Error", "N/A", "N/A"))
    
    def _format_size(self, size: int) -> str:
        """Format file size in human-readable format."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def _format_time(self, timestamp: float) -> str:
        """Format timestamp in human-readable format."""
        import datetime
        dt = datetime.datetime.fromtimestamp(timestamp)
        return dt.strftime("%Y-%m-%d %H:%M")
    
    def create_new_bucket(self):
        """Create a new bucket."""
        name = simpledialog.askstring("New Bucket", "Enter bucket name:")
        if name:
            name = name.strip()
            if not name:
                messagebox.showwarning("Warning", "Bucket name cannot be empty")
                return
            
            success = self.main_window.config.add_bucket(name)
            if success:
                self.refresh()
                # Select the new bucket
                for i in range(self.bucket_listbox.size()):
                    if self.bucket_listbox.get(i).startswith(name):
                        self.bucket_listbox.selection_set(i)
                        self.on_bucket_select(None)
                        break
                messagebox.showinfo("Success", f"Bucket '{name}' created")
            else:
                messagebox.showwarning("Warning", f"Bucket '{name}' already exists")
    
    def delete_bucket(self):
        """Delete the selected bucket."""
        if not self.current_bucket:
            messagebox.showwarning("Warning", "Please select a bucket to delete")
            return
        
        # Confirm deletion
        result = messagebox.askyesno("Confirm Delete", 
                                   f"Are you sure you want to delete bucket '{self.current_bucket}'?\n"
                                   f"This will not delete the actual files.")
        if result:
            success = self.main_window.config.remove_bucket(self.current_bucket)
            if success:
                self.current_bucket = None
                self.refresh()
                messagebox.showinfo("Success", "Bucket deleted")
            else:
                messagebox.showerror("Error", "Failed to delete bucket")
    
    def export_bucket(self):
        """Export the selected bucket."""
        if not self.current_bucket:
            messagebox.showwarning("Warning", "Please select a bucket to export")
            return
        
        self.main_window.export_bucket(self.current_bucket)
    
    def on_bucket_select(self, event):
        """Handle bucket selection."""
        selection = self.bucket_listbox.curselection()
        if selection:
            display_text = self.bucket_listbox.get(selection[0])
            bucket_name = display_text.split(' (')[0]  # Extract name before file count
            self.current_bucket = bucket_name
            self._populate_file_list()
        else:
            self.current_bucket = None
            self._populate_file_list()
    
    def on_bucket_double_click(self, event):
        """Handle bucket double-click."""
        # Could open bucket properties or rename dialog
        pass
    
    def on_file_double_click(self, event):
        """Handle file double-click."""
        self.analyze_selected_file()
    
    def on_file_right_click(self, event):
        """Handle file right-click."""
        # Select the item under cursor
        item = self.file_tree.identify_row(event.y)
        if item:
            self.file_tree.selection_set(item)
            self.file_context_menu.post(event.x_root, event.y_root)
    
    def get_selected_file(self) -> Optional[Path]:
        """Get the currently selected file."""
        selection = self.file_tree.selection()
        if selection:
            item = self.file_tree.item(selection[0])
            values = item.get('values', [])
            if values and values[0] != "Error":
                return Path(values[0])
        return None
    
    def remove_selected_file(self):
        """Remove the selected file from the bucket."""
        file_path = self.get_selected_file()
        if not file_path:
            messagebox.showwarning("Warning", "Please select a file to remove")
            return
        
        if not self.current_bucket:
            messagebox.showwarning("Warning", "No bucket selected")
            return
        
        # Confirm removal
        result = messagebox.askyesno("Confirm Remove", 
                                   f"Remove '{file_path.name}' from bucket '{self.current_bucket}'?\n"
                                   f"This will not delete the actual file.")
        if result:
            self.main_window.remove_from_bucket(file_path, self.current_bucket)
    
    def analyze_selected_file(self):
        """Analyze the selected file."""
        file_path = self.get_selected_file()
        if file_path and file_path.suffix == '.py':
            self.main_window.analyze_file(file_path)
        elif file_path:
            messagebox.showwarning("Warning", "Please select a Python file")
        else:
            messagebox.showwarning("Warning", "Please select a file")
    
    def open_selected_file(self):
        """Open the selected file in default editor."""
        file_path = self.get_selected_file()
        if file_path:
            try:
                import subprocess
                import sys
                import os
                if sys.platform == "win32":
                    os.startfile(file_path)
                elif sys.platform == "darwin":
                    subprocess.run(["open", file_path])
                else:
                    subprocess.run(["xdg-open", file_path])
            except Exception as e:
                messagebox.showerror("Error", f"Failed to open file: {e}")
        else:
            messagebox.showwarning("Warning", "Please select a file")
    
    def show_selected_file(self):
        """Show the selected file in file explorer."""
        file_path = self.get_selected_file()
        if file_path:
            try:
                import subprocess
                import sys
                if sys.platform == "win32":
                    subprocess.run(["explorer", "/select,", str(file_path)])
                elif sys.platform == "darwin":
                    subprocess.run(["open", "-R", str(file_path)])
                else:
                    subprocess.run(["xdg-open", str(file_path.parent)])
            except Exception as e:
                messagebox.showerror("Error", f"Failed to show in explorer: {e}")
        else:
            messagebox.showwarning("Warning", "Please select a file")
