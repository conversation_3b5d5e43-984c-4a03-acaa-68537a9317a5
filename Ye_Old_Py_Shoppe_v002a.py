
# Ye Old V1 Py Shoppe v0.02a — The Whispering Window
# Requires: pip install customtkinter

import os
import json
import ast
import customtkinter as ctk
from tkinter import filedialog
from pathlib import Path
import textwrap

DB_FILE = "project_db.json"
SHOPPING_LIST_FILE = "shopping_list.txt"

def load_state():
    if os.path.exists(DB_FILE):
        with open(DB_FILE, "r") as f:
            return json.load(f)
    return {"projects": {}, "shopping_list": []}

def save_state():
    with open(DB_FILE, "w") as f:
        json.dump(app_state, f, indent=2)

app_state = load_state()
ctk.set_appearance_mode("System")
ctk.set_default_color_theme("blue")

class PyShoppeApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("Ye Old V1 Py Shoppe v0.02a — The Whispering Window")
        self.geometry("1300x800")
        self.protocol("WM_DELETE_WINDOW", self.on_close)
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)
        self.sidebar = Sidebar(self)
        self.sidebar.grid(row=0, column=0, sticky="ns")
        self.project_view = ProjectBucketPanel(self)
        self.project_view.grid(row=0, column=1, sticky="nsew")
        self.preview_pane = ShopWindow(self)
        self.preview_pane.grid(row=0, column=2, sticky="ns")

    def on_close(self):
        save_state()
        self.destroy()

class Sidebar(ctk.CTkFrame):
    def __init__(self, master):
        super().__init__(master, width=200)
        self.pack_propagate(False)
        ctk.CTkLabel(self, text="Py Shoppe", font=("Segoe UI", 20, "bold")).pack(pady=10)
        self.project_name_entry = ctk.CTkEntry(self, placeholder_text="New Project Name")
        self.project_name_entry.pack(pady=5, padx=10)
        ctk.CTkButton(self, text="Create Project", command=self.create_project).pack(pady=5)
        ctk.CTkButton(self, text="Add Files", command=self.add_files_to_project).pack(pady=5)
        ctk.CTkButton(self, text="Scan Widgets", command=self.scan_widgets).pack(pady=20)
        ctk.CTkButton(self, text="Save Shopping List", command=self.save_shopping_list).pack(pady=5)

    def create_project(self):
        name = self.project_name_entry.get().strip()
        if name and name not in app_state["projects"]:
            app_state["projects"][name] = {"files": []}
            self.master.project_view.update_project_list()

    def add_files_to_project(self):
        files = filedialog.askopenfilenames(filetypes=[("Python files", "*.py")])
        current_project = self.master.project_view.get_selected_project()
        if current_project and files:
            for f in files:
                if f not in app_state["projects"][current_project]["files"]:
                    app_state["projects"][current_project]["files"].append(f)
            self.master.project_view.update_file_list(current_project)

    def scan_widgets(self):
        project = self.master.project_view.get_selected_project()
        if not project:
            return
        files = app_state["projects"][project]["files"]
        results = []
        for file in files:
            try:
                with open(file, "r", encoding="utf-8") as f:
                    lines = f.readlines()
                    tree = ast.parse("".join(lines))
                    for node in ast.walk(tree):
                        if isinstance(node, ast.Call) and isinstance(node.func, ast.Name):
                            if node.func.id in ["Button", "Label", "Entry", "Checkbutton"]:
                                block = textwrap.dedent("".join(lines[node.lineno-1:node.lineno+2]))
                                results.append({
                                    "file": file,
                                    "widget": node.func.id,
                                    "lineno": node.lineno,
                                    "code": block.strip()
                                })
            except Exception as e:
                results.append({"file": file, "error": str(e)})
        self.master.preview_pane.display_results(results)

    def save_shopping_list(self):
        with open(SHOPPING_LIST_FILE, "w", encoding="utf-8") as f:
            for item in app_state["shopping_list"]:
                f.write(f"# From: {item['file']} (Line {item['lineno']})\n")
                f.write(item["code"] + "\n\n")

class ProjectBucketPanel(ctk.CTkFrame):
    def __init__(self, master):
        super().__init__(master)
        self.selected_project = None
        self.project_listbox = ctk.CTkOptionMenu(self, values=[], command=self.select_project)
        self.project_listbox.pack(pady=10, padx=10)
        self.file_listbox = ctk.CTkTextbox(self, width=400, height=600, wrap="none")
        self.file_listbox.pack(pady=5, padx=10, fill="both", expand=True)
        self.update_project_list()

    def update_project_list(self):
        projects = list(app_state["projects"].keys())
        if not projects:
            self.project_listbox.configure(values=["<No Projects>"])
        else:
            self.project_listbox.configure(values=projects)
            self.project_listbox.set(projects[0])
            self.select_project(projects[0])

    def select_project(self, project_name):
        self.selected_project = project_name
        self.update_file_list(project_name)

    def update_file_list(self, project_name):
        self.file_listbox.delete("1.0", "end")
        files = app_state["projects"][project_name]["files"]
        for f in files:
            self.file_listbox.insert("end", f + "\n")

    def get_selected_project(self):
        return self.selected_project

class ShopWindow(ctk.CTkFrame):
    def __init__(self, master):
        super().__init__(master, width=400)
        self.pack_propagate(False)
        ctk.CTkLabel(self, text="🪞 Shop Window", font=("Segoe UI", 16, "bold")).pack(pady=10)
        self.preview_area = ctk.CTkScrollableFrame(self, width=380, height=700)
        self.preview_area.pack(padx=10, pady=5, fill="both", expand=True)

    def display_results(self, results):
        for widget in self.preview_area.winfo_children():
            widget.destroy()
        for r in results:
            frame = ctk.CTkFrame(self.preview_area)
            frame.pack(padx=5, pady=5, fill="x")

            if "error" in r:
                ctk.CTkLabel(frame, text=f"❌ {r['file']}", text_color="red").pack(anchor="w")
                ctk.CTkLabel(frame, text=r["error"]).pack(anchor="w")
            else:
                title = f"{Path(r['file']).name} → {r['widget']} (Line {r['lineno']})"
                ctk.CTkLabel(frame, text=title).pack(anchor="w")

                # Simulated widget
                if r['widget'] == "Button":
                    btn = ctk.CTkButton(frame, text="Sim: Play/Pause", fg_color="green")
                    btn.pack(padx=5, pady=2)
                elif r['widget'] == "Label":
                    lbl = ctk.CTkLabel(frame, text="Sim: Label Text")
                    lbl.pack(padx=5, pady=2)
                elif r['widget'] == "Entry":
                    ent = ctk.CTkEntry(frame)
                    ent.pack(padx=5, pady=2)
                elif r['widget'] == "Checkbutton":
                    chk = ctk.CTkCheckBox(frame, text="Sim Check")
                    chk.pack(padx=5, pady=2)

                # Forensics note
                tip = "🤖 Advisor: This widget may be missing `.pack()` or use a dynamic variable."
                ctk.CTkLabel(frame, text=tip, font=("Segoe UI", 10), text_color="gray").pack(anchor="w", padx=5)

                # Code & actions
                codebox = ctk.CTkTextbox(frame, height=60, wrap="word")
                codebox.insert("1.0", r["code"])
                codebox.configure(state="disabled")
                codebox.pack(padx=5, pady=2)

                action_frame = ctk.CTkFrame(frame, fg_color="transparent")
                action_frame.pack(fill="x", pady=2)
                ctk.CTkButton(action_frame, text="📋 Copy", width=80,
                              command=lambda c=r["code"]: self.clip(c)).pack(side="left", padx=5)
                ctk.CTkButton(action_frame, text="🛒 Add", width=80,
                              command=lambda r=r: self.save_to_list(r)).pack(side="left", padx=5)

    def save_to_list(self, result):
        app_state["shopping_list"].append({
            "file": result["file"],
            "lineno": result["lineno"],
            "code": result["code"]
        })

    def clip(self, text):
        self.clipboard_clear()
        self.clipboard_append(text)
        self.update()

app = PyShoppeApp()
app.mainloop()
