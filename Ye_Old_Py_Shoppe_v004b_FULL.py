
"""
Ye Old Py Shoppe v0.04b FULL
Includes: Buckets, Side-by-Side, Familiar Selector Scaffolding, Navigation Tools
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import hashlib
import os
import zipfile
import threading
import subprocess

class YeOldPyShoppeApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Ye Old Py Shoppe 🪄 v0.04b")
        self.familiars = ["Speccles", "Pazuzu", "Grimm", "LeVay", "Nyx"]
        self.current_familiar = tk.StringVar(value="Speccles")
        self.build_ui()

    def build_ui(self):
        # Familiar Selector
        top_frame = tk.Frame(self.root)
        top_frame.pack(fill="x")
        tk.Label(top_frame, text="Familiar:").pack(side="left")
        ttk.Combobox(top_frame, textvariable=self.current_familiar, values=self.familiars, state="readonly").pack(side="left")

        # Add Folder Button
        tk.Button(top_frame, text="📂 Add Folder", command=self.add_folder).pack(side="left", padx=5)

        # Search Bar
        self.search_var = tk.StringVar()
        tk.Entry(top_frame, textvariable=self.search_var, width=40).pack(side="left", padx=5)
        tk.Button(top_frame, text="Ask Speccles", command=self.ask_speccles).pack(side="left")

        # Results Frame
        self.results = tk.Text(self.root, height=25)
        self.results.pack(fill="both", expand=True)

    def add_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.results.insert("end", f"📁 Scanning folder: {folder}\n")
            threading.Thread(target=self.scan_folder, args=(folder,), daemon=True).start()

    def scan_folder(self, folder):
        for dirpath, dirnames, filenames in os.walk(folder):
            for file in filenames:
                if file.endswith(".py"):
                    full_path = os.path.join(dirpath, file)
                    with open(full_path, "r", encoding="utf-8", errors="ignore") as f:
                        try:
                            content = f.read()
                            sha = hashlib.sha256(content.encode()).hexdigest()
                            self.results.insert("end", f"📜 {file} | SHA256: {sha[:8]}...\n")
                        except Exception as e:
                            self.results.insert("end", f"⚠️ Error reading {file}: {e}\n")

    def ask_speccles(self):
        query = self.search_var.get()
        if query.strip():
            self.results.insert("end", f"🧠 Speccles heard you: \"{query}\"\n")
        else:
            self.results.insert("end", "🐾 Speccles is waiting for your question...\n")

if __name__ == "__main__":
    root = tk.Tk()
    app = YeOldPyShoppeApp(root)
    root.mainloop()
