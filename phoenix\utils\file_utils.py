"""
File utility functions for Phoenix application.
"""

import os
import shutil
import zipfile
import logging
from pathlib import Path
from typing import List, Optional, Generator
from datetime import datetime

logger = logging.getLogger(__name__)


def create_backup(file_path: Path, backup_dir: Optional[Path] = None) -> Path:
    """Create a backup of a file.
    
    Args:
        file_path: Path to the file to backup
        backup_dir: Directory to store backup. Defaults to same directory as file.
        
    Returns:
        Path to the backup file
        
    Raises:
        FileNotFoundError: If the source file doesn't exist
        PermissionError: If unable to create backup
    """
    if not file_path.exists():
        raise FileNotFoundError(f"Source file not found: {file_path}")
    
    if backup_dir is None:
        backup_dir = file_path.parent
    else:
        backup_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"{file_path.stem}_{timestamp}.bak"
    backup_path = backup_dir / backup_name
    
    try:
        shutil.copy2(file_path, backup_path)
        logger.info(f"Created backup: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Failed to create backup: {e}")
        raise


def restore_from_backup(backup_path: Path, target_path: Path) -> bool:
    """Restore a file from backup.
    
    Args:
        backup_path: Path to the backup file
        target_path: Path where to restore the file
        
    Returns:
        True if restoration was successful, False otherwise
    """
    try:
        if not backup_path.exists():
            logger.error(f"Backup file not found: {backup_path}")
            return False
        
        shutil.copy2(backup_path, target_path)
        logger.info(f"Restored from backup: {backup_path} -> {target_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to restore from backup: {e}")
        return False


def find_python_files(directory: Path, recursive: bool = True) -> Generator[Path, None, None]:
    """Find all Python files in a directory.
    
    Args:
        directory: Directory to search
        recursive: Whether to search recursively
        
    Yields:
        Path objects for Python files found
    """
    if not directory.exists() or not directory.is_dir():
        logger.warning(f"Directory not found or not a directory: {directory}")
        return
    
    pattern = "**/*.py" if recursive else "*.py"
    
    try:
        for file_path in directory.glob(pattern):
            if file_path.is_file():
                yield file_path
    except Exception as e:
        logger.error(f"Error searching for Python files: {e}")


def create_project_zip(file_paths: List[Path], output_path: Path, 
                      base_dir: Optional[Path] = None) -> bool:
    """Create a ZIP file containing the specified files.
    
    Args:
        file_paths: List of file paths to include
        output_path: Path for the output ZIP file
        base_dir: Base directory for relative paths in ZIP
        
    Returns:
        True if ZIP creation was successful, False otherwise
    """
    try:
        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in file_paths:
                if not file_path.exists():
                    logger.warning(f"File not found, skipping: {file_path}")
                    continue
                
                if base_dir:
                    try:
                        arcname = file_path.relative_to(base_dir)
                    except ValueError:
                        # File is not under base_dir, use just the filename
                        arcname = file_path.name
                else:
                    arcname = file_path.name
                
                zipf.write(file_path, arcname)
                logger.debug(f"Added to ZIP: {file_path} as {arcname}")
        
        logger.info(f"Created ZIP file: {output_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to create ZIP file: {e}")
        return False


def safe_read_file(file_path: Path, encoding: str = 'utf-8') -> Optional[str]:
    """Safely read a text file.
    
    Args:
        file_path: Path to the file
        encoding: File encoding
        
    Returns:
        File content as string, or None if reading failed
    """
    try:
        return file_path.read_text(encoding=encoding)
    except Exception as e:
        logger.error(f"Failed to read file {file_path}: {e}")
        return None


def safe_write_file(file_path: Path, content: str, encoding: str = 'utf-8',
                   create_backup: bool = True) -> bool:
    """Safely write content to a file.
    
    Args:
        file_path: Path to the file
        content: Content to write
        encoding: File encoding
        create_backup: Whether to create a backup if file exists
        
    Returns:
        True if writing was successful, False otherwise
    """
    try:
        # Create backup if file exists and backup is requested
        if create_backup and file_path.exists():
            backup_path = create_backup(file_path)
            logger.debug(f"Created backup before writing: {backup_path}")
        
        file_path.write_text(content, encoding=encoding)
        logger.debug(f"Successfully wrote file: {file_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to write file {file_path}: {e}")
        return False


def get_file_stats(file_path: Path) -> dict:
    """Get file statistics.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Dictionary with file statistics
    """
    try:
        stat = file_path.stat()
        return {
            'size': stat.st_size,
            'modified': datetime.fromtimestamp(stat.st_mtime),
            'created': datetime.fromtimestamp(stat.st_ctime),
            'is_file': file_path.is_file(),
            'is_dir': file_path.is_dir(),
            'exists': file_path.exists()
        }
    except Exception as e:
        logger.error(f"Failed to get file stats for {file_path}: {e}")
        return {}


def ensure_directory(directory: Path) -> bool:
    """Ensure a directory exists, creating it if necessary.
    
    Args:
        directory: Path to the directory
        
    Returns:
        True if directory exists or was created successfully
    """
    try:
        directory.mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"Failed to create directory {directory}: {e}")
        return False
