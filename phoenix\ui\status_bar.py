"""
Status bar for Phoenix application.
"""

import tkinter as tk
from tkinter import ttk
import logging
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .main_window import MainWindow

logger = logging.getLogger(__name__)


class StatusBar(ttk.Frame):
    """Application status bar."""
    
    def __init__(self, parent, main_window: 'MainWindow'):
        super().__init__(parent, relief=tk.SUNKEN, borderwidth=1)
        self.main_window = main_window
        
        self._create_widgets()
        self._update_status()
    
    def _create_widgets(self):
        """Create status bar widgets."""
        # Main status message
        self.status_label = ttk.Label(self, text="Ready", anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=2)
        
        # Separator
        ttk.Separator(self, orient=tk.VERTICAL).pack(side=tk.RIGHT, fill=tk.Y, padx=2)
        
        # File count
        self.file_count_label = ttk.Label(self, text="Files: 0", anchor=tk.E, width=10)
        self.file_count_label.pack(side=tk.RIGHT, padx=5, pady=2)
        
        # Separator
        ttk.Separator(self, orient=tk.VERTICAL).pack(side=tk.RIGHT, fill=tk.Y, padx=2)
        
        # Bucket count
        self.bucket_count_label = ttk.Label(self, text="Buckets: 0", anchor=tk.E, width=12)
        self.bucket_count_label.pack(side=tk.RIGHT, padx=5, pady=2)
        
        # Separator
        ttk.Separator(self, orient=tk.VERTICAL).pack(side=tk.RIGHT, fill=tk.Y, padx=2)
        
        # Current familiar
        self.familiar_label = ttk.Label(self, text="🔥 Phoenix", anchor=tk.E, width=15)
        self.familiar_label.pack(side=tk.RIGHT, padx=5, pady=2)
    
    def set_message(self, message: str, timeout: int = 5000):
        """Set the status message.
        
        Args:
            message: Message to display
            timeout: Time in milliseconds before reverting to default message
        """
        self.status_label.config(text=message)
        
        # Revert to default message after timeout
        if timeout > 0:
            self.after(timeout, lambda: self.status_label.config(text="Ready"))
    
    def update_familiar(self, familiar_name: str):
        """Update the familiar display."""
        familiar_icons = {
            "phoenix": "🔥 Phoenix",
            "grey_man": "👤 Grey Man",
            "grimm": "🌙 Grimm",
            "levay": "😈 LeVay",
            "technical": "🔧 Technical"
        }
        display_text = familiar_icons.get(familiar_name.lower(), f"✨ {familiar_name}")
        self.familiar_label.config(text=display_text)
    
    def _update_status(self):
        """Update status information."""
        try:
            # Update bucket count
            buckets = self.main_window.config.get_buckets()
            bucket_count = len(buckets)
            self.bucket_count_label.config(text=f"Buckets: {bucket_count}")
            
            # Update file count (total files in all buckets)
            total_files = sum(len(files) for files in buckets.values())
            self.file_count_label.config(text=f"Files: {total_files}")
            
            # Update familiar
            familiar = self.main_window.config.get_setting("familiar_mode", "phoenix")
            self.update_familiar(familiar)
                
        except Exception as e:
            logger.error(f"Failed to update status: {e}")
        
        # Schedule next update
        self.after(2000, self._update_status)  # Update every 2 seconds
