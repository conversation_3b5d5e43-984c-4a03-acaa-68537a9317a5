
"""
Ye Old Py Shoppe v0.14 – Snippet Manager & Smarter Comment Tools
Features:
- Familiar personality affects comment tone (<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, etc.)
- Safe comment removal using `tokenize.untokenize()` (200 IQ strip)
- 🧾 Save extracted classes/methods to Snippet Library
- Snippet Library UI browser with preview + insert
- Undo/redo support for comment changes
- Spellbook and AI whispering toggle logic retained
- All features from v0.13 retained
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog, ttk
import os, sys, subprocess, threading, zipfile, json, ast, difflib, time, tokenize, io
from pathlib import Path

# In class YeOldPyShoppeApp (update _strip_comments with smarter tokenizer logic)

    def _strip_comments(self, path):
        try:
            code = Path(path).read_text()
            tokens = tokenize.generate_tokens(io.StringIO(code).readline)
            new_tokens = []
            for tok in tokens:
                if tok.type != tokenize.COMMENT:
                    new_tokens.append(tok)
            cleaned = tokenize.untokenize(new_tokens)
            self._backup_and_save(path, cleaned)
            messagebox.showinfo("Stripped", f"Comments stripped from {Path(path).name}")
        except Exception as e:
            messagebox.showerror("Strip Error", str(e))

# (Begin Phase 14 – Snippet Browser UI)

    def _open_snippet_browser(self):
        win = ctk.CTkToplevel(self)
        win.title("Snippet Library 🧾")
        win.geometry("700x500")
        listbox = tk.Listbox(win)
        listbox.pack(side="left", fill="y")

        preview = tk.Text(win, wrap="none")
        preview.pack(side="right", fill="both", expand=True)

        snippets = list(Path("snippets").glob("*.py"))
        for s in snippets:
            listbox.insert("end", s.name)

        def load_preview(event):
            sel = listbox.curselection()
            if sel:
                fname = snippets[sel[0]]
                preview.delete("1.0", "end")
                preview.insert("1.0", Path(fname).read_text())

        listbox.bind("<<ListboxSelect>>", load_preview)

        ctk.CTkButton(win, text="Insert to Project", command=lambda: self._insert_snippet(Path(snippets[listbox.curselection()[0]]) if listbox.curselection() else None)).pack(pady=5)

    def _insert_snippet(self, path):
        if not path: return
        try:
            snippet = Path(path).read_text()
            # You can change this to append to a temp file or clipboard
            with open("injected_snippet.py", "a") as f:
                f.write(f"\n\n# Inserted from snippet: {path.name}\n" + snippet)
            messagebox.showinfo("Inserted", f"Snippet {path.name} inserted to project file.")
        except Exception as e:
            messagebox.showerror("Insert Error", str(e))

ctk.CTkButton(top, text="🧾 Snippet Library", command=self._open_snippet_browser).pack(side="left", padx=5)

# (Begin Phase 15 – Project Composer)

    def _export_project_zip(self):
        if not self.current_bucket:
            messagebox.showwarning("No Bucket", "Select a bucket to export.")
            return

        paths = self.db.get("buckets", {}).get(self.current_bucket, [])
        if not paths:
            messagebox.showinfo("Empty Bucket", "No files to export.")
            return

        dest = filedialog.asksaveasfilename(defaultextension=".zip", filetypes=[("Zip files", "*.zip")])
        if not dest:
            return

        try:
            with zipfile.ZipFile(dest, "w", compression=zipfile.ZIP_DEFLATED) as zf:
                for f in paths:
                    if os.path.isfile(f):
                        arcname = os.path.basename(f)
                        zf.write(f, arcname)
            messagebox.showinfo("Exported", f"Project exported to {dest}")
        except Exception as e:
            messagebox.showerror("Export Error", str(e))

    # Button for Project Export
    ctk.CTkButton(top, text="📦 Export Project ZIP", command=self._export_project_zip).pack(side="left", padx=5)
