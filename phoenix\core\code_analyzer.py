"""
Code analysis functionality for Phoenix application.
"""

import ast
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class CodeElement:
    """Represents a code element (class, function, etc.)."""
    name: str
    type: str  # 'class', 'function', 'method'
    line_number: int
    docstring: Optional[str] = None
    parent: Optional[str] = None  # For methods, the parent class name


@dataclass
class FileAnalysis:
    """Results of analyzing a Python file."""
    file_path: Path
    classes: List[CodeElement]
    functions: List[CodeElement]
    methods: List[CodeElement]
    imports: List[str]
    total_lines: int
    error: Optional[str] = None


class CodeAnalyzer:
    """Analyzes Python code using AST parsing."""
    
    def __init__(self):
        self.cache: Dict[str, FileAnalysis] = {}
    
    def analyze_file(self, file_path: Path, use_cache: bool = True) -> FileAnalysis:
        """Analyze a Python file and extract code elements.
        
        Args:
            file_path: Path to the Python file
            use_cache: Whether to use cached results
            
        Returns:
            FileAnalysis object with extracted information
        """
        file_key = str(file_path.absolute())
        
        # Check cache first
        if use_cache and file_key in self.cache:
            cached = self.cache[file_key]
            # Verify file hasn't been modified
            try:
                if file_path.stat().st_mtime <= cached.file_path.stat().st_mtime:
                    return cached
            except:
                pass  # File might not exist anymore, continue with fresh analysis
        
        analysis = self._analyze_file_fresh(file_path)
        self.cache[file_key] = analysis
        return analysis
    
    def _analyze_file_fresh(self, file_path: Path) -> FileAnalysis:
        """Perform fresh analysis of a Python file."""
        try:
            content = file_path.read_text(encoding='utf-8')
            tree = ast.parse(content, filename=str(file_path))
            
            classes = []
            functions = []
            methods = []
            imports = []
            
            # Count total lines
            total_lines = len(content.splitlines())
            
            # Walk the AST
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    classes.append(self._extract_class_info(node))
                elif isinstance(node, ast.FunctionDef):
                    # Determine if it's a method or standalone function
                    parent_class = self._find_parent_class(node, tree)
                    if parent_class:
                        methods.append(self._extract_method_info(node, parent_class))
                    else:
                        functions.append(self._extract_function_info(node))
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    imports.extend(self._extract_import_info(node))
            
            return FileAnalysis(
                file_path=file_path,
                classes=classes,
                functions=functions,
                methods=methods,
                imports=imports,
                total_lines=total_lines
            )
            
        except Exception as e:
            logger.error(f"Failed to analyze file {file_path}: {e}")
            return FileAnalysis(
                file_path=file_path,
                classes=[],
                functions=[],
                methods=[],
                imports=[],
                total_lines=0,
                error=str(e)
            )
    
    def _extract_class_info(self, node: ast.ClassDef) -> CodeElement:
        """Extract information from a class definition."""
        docstring = ast.get_docstring(node)
        return CodeElement(
            name=node.name,
            type='class',
            line_number=node.lineno,
            docstring=docstring
        )
    
    def _extract_function_info(self, node: ast.FunctionDef) -> CodeElement:
        """Extract information from a function definition."""
        docstring = ast.get_docstring(node)
        return CodeElement(
            name=node.name,
            type='function',
            line_number=node.lineno,
            docstring=docstring
        )
    
    def _extract_method_info(self, node: ast.FunctionDef, parent_class: str) -> CodeElement:
        """Extract information from a method definition."""
        docstring = ast.get_docstring(node)
        return CodeElement(
            name=node.name,
            type='method',
            line_number=node.lineno,
            docstring=docstring,
            parent=parent_class
        )
    
    def _extract_import_info(self, node: ast.AST) -> List[str]:
        """Extract import information."""
        imports = []
        
        if isinstance(node, ast.Import):
            for alias in node.names:
                imports.append(alias.name)
        elif isinstance(node, ast.ImportFrom):
            module = node.module or ''
            for alias in node.names:
                if alias.name == '*':
                    imports.append(f"from {module} import *")
                else:
                    imports.append(f"from {module} import {alias.name}")
        
        return imports
    
    def _find_parent_class(self, func_node: ast.FunctionDef, tree: ast.AST) -> Optional[str]:
        """Find the parent class of a function node."""
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                for child in node.body:
                    if child is func_node:
                        return node.name
        return None
    
    def analyze_directory(self, directory: Path, recursive: bool = True) -> List[FileAnalysis]:
        """Analyze all Python files in a directory.
        
        Args:
            directory: Directory to analyze
            recursive: Whether to search recursively
            
        Returns:
            List of FileAnalysis objects
        """
        results = []
        
        if not directory.exists() or not directory.is_dir():
            logger.warning(f"Directory not found: {directory}")
            return results
        
        pattern = "**/*.py" if recursive else "*.py"
        
        try:
            for file_path in directory.glob(pattern):
                if file_path.is_file():
                    analysis = self.analyze_file(file_path)
                    results.append(analysis)
        except Exception as e:
            logger.error(f"Error analyzing directory {directory}: {e}")
        
        return results
    
    def get_summary(self, analyses: List[FileAnalysis]) -> Dict[str, Any]:
        """Get a summary of multiple file analyses.
        
        Args:
            analyses: List of FileAnalysis objects
            
        Returns:
            Summary dictionary
        """
        total_files = len(analyses)
        total_classes = sum(len(a.classes) for a in analyses)
        total_functions = sum(len(a.functions) for a in analyses)
        total_methods = sum(len(a.methods) for a in analyses)
        total_lines = sum(a.total_lines for a in analyses)
        errors = [a for a in analyses if a.error]
        
        return {
            'total_files': total_files,
            'total_classes': total_classes,
            'total_functions': total_functions,
            'total_methods': total_methods,
            'total_lines': total_lines,
            'files_with_errors': len(errors),
            'error_files': [str(a.file_path) for a in errors]
        }
    
    def clear_cache(self) -> None:
        """Clear the analysis cache."""
        self.cache.clear()
        logger.info("Analysis cache cleared")
    
    def export_to_json(self, analyses: List[FileAnalysis]) -> Dict[str, Any]:
        """Export analyses to JSON-serializable format.
        
        Args:
            analyses: List of FileAnalysis objects
            
        Returns:
            JSON-serializable dictionary
        """
        result = {
            'summary': self.get_summary(analyses),
            'files': []
        }
        
        for analysis in analyses:
            file_data = {
                'path': str(analysis.file_path),
                'total_lines': analysis.total_lines,
                'error': analysis.error,
                'classes': [
                    {
                        'name': cls.name,
                        'line': cls.line_number,
                        'docstring': cls.docstring
                    }
                    for cls in analysis.classes
                ],
                'functions': [
                    {
                        'name': func.name,
                        'line': func.line_number,
                        'docstring': func.docstring
                    }
                    for func in analysis.functions
                ],
                'methods': [
                    {
                        'name': method.name,
                        'line': method.line_number,
                        'parent': method.parent,
                        'docstring': method.docstring
                    }
                    for method in analysis.methods
                ],
                'imports': analysis.imports
            }
            result['files'].append(file_data)
        
        return result
