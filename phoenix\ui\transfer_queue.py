"""
Transfer queue component for showing operations (FileZilla-style).
"""

import tkinter as tk
from tkinter import ttk
import logging
from datetime import datetime
from typing import List, Dict, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from .main_window import MainWindow

logger = logging.getLogger(__name__)


class TransferQueue(ttk.Frame):
    """Queue showing file operations and their status."""
    
    def __init__(self, parent, main_window: 'MainWindow'):
        super().__init__(parent)
        self.main_window = main_window
        self.operations: List[Dict[str, Any]] = []
        
        self._setup_ui()
        self._setup_bindings()
    
    def _setup_ui(self):
        """Set up the transfer queue UI."""
        # Toolbar
        toolbar = ttk.Frame(self)
        toolbar.pack(fill=tk.X, padx=2, pady=2)
        
        ttk.Button(toolbar, text="Clear Completed", command=self.clear_completed).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Clear All", command=self.clear_all).pack(side=tk.LEFT, padx=2)
        
        # Status label
        self.status_label = ttk.Label(toolbar, text="Ready")
        self.status_label.pack(side=tk.RIGHT, padx=10)
        
        # Operations list
        columns = ("time", "operation", "file", "target", "status")
        self.tree = ttk.Treeview(self, columns=columns, show="headings", height=6)
        
        # Configure columns
        self.tree.heading("time", text="Time")
        self.tree.column("time", width=120, minwidth=80)
        self.tree.heading("operation", text="Operation")
        self.tree.column("operation", width=120, minwidth=80)
        self.tree.heading("file", text="File")
        self.tree.column("file", width=200, minwidth=100)
        self.tree.heading("target", text="Target")
        self.tree.column("target", width=150, minwidth=80)
        self.tree.heading("status", text="Status")
        self.tree.column("status", width=100, minwidth=60)
        
        # Scrollbars
        v_scroll = ttk.Scrollbar(self, orient=tk.VERTICAL, command=self.tree.yview)
        h_scroll = ttk.Scrollbar(self, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scroll.set, xscrollcommand=h_scroll.set)
        
        # Pack components
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        h_scroll.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Context menu
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="Remove Entry", command=self.remove_selected)
        self.context_menu.add_command(label="Copy Details", command=self.copy_details)
    
    def _setup_bindings(self):
        """Set up event bindings."""
        self.tree.bind("<Button-3>", self.on_right_click)
        self.tree.bind("<Delete>", lambda e: self.remove_selected())
    
    def add_operation(self, operation_type: str, file_path=None, target=None, status: str = "Completed"):
        """Add an operation to the queue.
        
        Args:
            operation_type: Type of operation (e.g., 'add_to_bucket', 'analyze_file')
            file_path: Path to the file involved
            target: Target (bucket name, output file, etc.)
            status: Operation status
        """
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        operation = {
            "time": timestamp,
            "operation": operation_type,
            "file": str(file_path.name) if file_path else "",
            "file_path": str(file_path) if file_path else "",
            "target": str(target) if target else "",
            "status": status
        }
        
        self.operations.append(operation)
        
        # Add to tree view
        icon = self._get_status_icon(status)
        self.tree.insert("", 0, values=(  # Insert at top
            timestamp,
            operation_type.replace("_", " ").title(),
            operation["file"],
            operation["target"],
            f"{icon} {status}"
        ))
        
        # Auto-scroll to top
        if self.tree.get_children():
            self.tree.see(self.tree.get_children()[0])
        
        # Update status
        self._update_status()
        
        # Limit queue size
        self._limit_queue_size()
    
    def _get_status_icon(self, status: str) -> str:
        """Get icon for operation status."""
        icons = {
            "Completed": "✅",
            "Failed": "❌",
            "In Progress": "⏳",
            "Queued": "⏸️",
            "Cancelled": "🚫"
        }
        return icons.get(status, "ℹ️")
    
    def _update_status(self):
        """Update the status label."""
        total = len(self.operations)
        completed = sum(1 for op in self.operations if op["status"] == "Completed")
        failed = sum(1 for op in self.operations if op["status"] == "Failed")
        
        if total == 0:
            status_text = "Ready"
        else:
            status_text = f"Total: {total} | Completed: {completed}"
            if failed > 0:
                status_text += f" | Failed: {failed}"
        
        self.status_label.config(text=status_text)
    
    def _limit_queue_size(self, max_size: int = 100):
        """Limit the queue size to prevent memory issues."""
        if len(self.operations) > max_size:
            # Remove oldest operations
            excess = len(self.operations) - max_size
            self.operations = self.operations[excess:]
            
            # Remove from tree view
            children = self.tree.get_children()
            for i in range(len(children) - max_size, len(children)):
                if i >= 0:
                    self.tree.delete(children[i])
    
    def clear_completed(self):
        """Clear completed operations from the queue."""
        # Remove completed operations from data
        self.operations = [op for op in self.operations if op["status"] != "Completed"]
        
        # Rebuild tree view
        self._rebuild_tree()
        self._update_status()
    
    def clear_all(self):
        """Clear all operations from the queue."""
        self.operations.clear()
        
        # Clear tree view
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        self._update_status()
    
    def _rebuild_tree(self):
        """Rebuild the tree view from operations data."""
        # Clear tree
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Add operations in reverse order (newest first)
        for operation in reversed(self.operations):
            icon = self._get_status_icon(operation["status"])
            self.tree.insert("", "end", values=(
                operation["time"],
                operation["operation"].replace("_", " ").title(),
                operation["file"],
                operation["target"],
                f"{icon} {operation['status']}"
            ))
    
    def on_right_click(self, event):
        """Handle right-click on tree item."""
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def remove_selected(self):
        """Remove the selected operation."""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            index = self.tree.index(item)
            
            # Remove from data (accounting for reverse order in tree)
            data_index = len(self.operations) - 1 - index
            if 0 <= data_index < len(self.operations):
                self.operations.pop(data_index)
            
            # Remove from tree
            self.tree.delete(item)
            self._update_status()
    
    def copy_details(self):
        """Copy operation details to clipboard."""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            values = self.tree.item(item)["values"]
            
            details = f"Time: {values[0]}\n"
            details += f"Operation: {values[1]}\n"
            details += f"File: {values[2]}\n"
            details += f"Target: {values[3]}\n"
            details += f"Status: {values[4]}"
            
            # Copy to clipboard
            self.clipboard_clear()
            self.clipboard_append(details)
    
    def get_operation_history(self) -> List[Dict[str, Any]]:
        """Get the complete operation history."""
        return self.operations.copy()
    
    def get_failed_operations(self) -> List[Dict[str, Any]]:
        """Get all failed operations."""
        return [op for op in self.operations if op["status"] == "Failed"]
    
    def export_history(self, file_path: str):
        """Export operation history to a file."""
        try:
            import json
            with open(file_path, 'w') as f:
                json.dump(self.operations, f, indent=2, default=str)
            return True
        except Exception as e:
            logger.error(f"Failed to export history: {e}")
            return False
