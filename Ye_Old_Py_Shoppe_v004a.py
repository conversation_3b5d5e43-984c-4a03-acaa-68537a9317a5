
# Ye Old Py Shoppe v0.04a – Whispers & Paws 🐾
# Requires: pip install customtkinter

import os, hashlib, zipfile
import customtkinter as ctk
from tkinter import filedialog
from pathlib import Path

BATCH_SIZE = 25

class YeOldPyShoppe(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("Ye Old Py Shoppe v0.04a – Whispers & Paws")
        self.geometry("1200x800")
        self.resizable(False, False)

        self.results = []
        self.batch_index = 0
        self.whisper_queue = []
        self.speccles_results = []

        self.build_ui()

    def build_ui(self):
        ctk.CTkLabel(self, text="Ye Old Py Shoppe", font=("Segoe UI", 24)).pack(pady=10)
        top_frame = ctk.CTkFrame(self)
        top_frame.pack(pady=5)

        ctk.CTkButton(top_frame, text="Add Folder", command=self.scan_folder).pack(side="left", padx=5)
        ctk.CTkEntry(top_frame, width=400, placeholder_text="Ask Speccles... (e.g., 'toggle button')", textvariable=ctk.StringVar()).pack(side="left", padx=5)
        ctk.CTkButton(top_frame, text="Search", command=self.speccles_search).pack(side="left", padx=5)

        self.preview_frame = ctk.CTkScrollableFrame(self, width=1100, height=600)
        self.preview_frame.pack(padx=10, pady=10)

        self.load_more_button = ctk.CTkButton(self, text="Load More", command=self.load_more_results)
        self.load_more_button.pack(pady=10)

    def scan_folder(self):
        folder = filedialog.askdirectory()
        if not folder: return
        self.results.clear()
        self.batch_index = 0
        self.preview_frame._parent_canvas.yview_moveto(0)

        for root, dirs, files in os.walk(folder):
            for file in files:
                if file.endswith(".py"):
                    full_path = os.path.join(root, file)
                    self.results.append({"type": "file", "path": full_path})
                elif file.endswith(".zip"):
                    zip_path = os.path.join(root, file)
                    self.scan_zip(zip_path)

        self.load_more_results()

    def scan_zip(self, zip_path):
        try:
            with zipfile.ZipFile(zip_path, 'r') as z:
                for zip_info in z.infolist():
                    if zip_info.filename.endswith(".py") and not zip_info.is_dir():
                        extracted = z.read(zip_info.filename).decode("utf-8", errors="ignore")
                        fake_path = f"archive://{zip_path}!{zip_info.filename}"
                        self.results.append({"type": "zip", "path": fake_path, "code": extracted})
        except Exception as e:
            self.results.append({"type": "error", "path": zip_path, "error": str(e)})

    def load_more_results(self):
        end = self.batch_index + BATCH_SIZE
        batch = self.results[self.batch_index:end]
        for item in batch:
            self.preview_result(item)
        self.batch_index += BATCH_SIZE
        if self.batch_index >= len(self.results):
            self.load_more_button.configure(state="disabled")

    def preview_result(self, item):
        box = ctk.CTkFrame(self.preview_frame)
        box.pack(padx=5, pady=5, fill="x")

        if item.get("type") == "error":
            ctk.CTkLabel(box, text=f"❌ Error: {item['path']}", text_color="red").pack(anchor="w")
            ctk.CTkLabel(box, text=item["error"]).pack(anchor="w")
            return

        ctk.CTkLabel(box, text=item['path'], font=("Consolas", 12)).pack(anchor="w")

        try:
            code = item["code"] if item["type"] == "zip" else open(item["path"], "r", encoding="utf-8", errors="ignore").read()
            display = code[:300] + ("..." if len(code) > 300 else "")
            txt = ctk.CTkTextbox(box, height=80, wrap="word")
            txt.insert("1.0", display)
            txt.configure(state="disabled")
            txt.pack(padx=5, pady=5)

            info = self.get_file_metadata(item, code)
            ctk.CTkLabel(box, text=info, font=("Consolas", 10), text_color="gray").pack(anchor="w")

            if not code.strip().endswith(":") and "def " not in code and "class " not in code:
                self.whisper_queue.append({"path": item["path"], "code": code})
        except Exception as e:
            ctk.CTkLabel(box, text=f"Error reading: {e}", text_color="red").pack(anchor="w")

    def get_file_metadata(self, item, code):
        try:
            size = len(code.encode("utf-8"))
            defs = code.count("def ")
            classes = code.count("class ")
            hashval = hashlib.sha256(code.encode("utf-8")).hexdigest()[:12]
            return f"SHA256: {hashval} | Size: {size} bytes | defs: {defs}, classes: {classes}"
        except:
            return "Metadata unavailable."

    def speccles_search(self):
        # For now, just simulate basic keyword match in file paths
        search_term = "toggle"
        self.speccles_results = [r for r in self.results if search_term.lower() in r["path"].lower()]
        self.preview_frame._parent_canvas.yview_moveto(0)
        for widget in self.preview_frame.winfo_children():
            widget.destroy()
        self.batch_index = 0
        self.results = self.speccles_results.copy()
        self.load_more_results()

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    app = YeOldPyShoppe()
    app.mainloop()
