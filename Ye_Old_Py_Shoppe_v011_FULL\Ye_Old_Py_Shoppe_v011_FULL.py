"""
Ye Old Py Shoppe v0.11 – Embedded Explorer Pane & Enhanced UI
Features:
- Folder Watch Pane with Browse…
- Embedded Explorer Pane (Treeview) with Name/Type/Size/Modified columns
- Context menu on tree items
- Labeled action buttons with tooltips
- Top-bar console shortcuts
- All prior v0.10 features retained (Dev Mode, animations, familiars, buckets, class extraction, spellbook stub, auto-whisper, export, run)
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog, ttk
import os, sys, subprocess, threading, zipfile, json, ast, difflib, time
from pathlib import Path

# Attempt OpenAI import
try:
    import openai
except ImportError:
    openai = None

# Config & database files
CONFIG_FILE    = "user_config.json"
PROJECT_DB     = "project_db.json"
INTEL_DB       = "intelligence_db.json"
DEV_MODE_FILE  = "dev_mode.json"
BATCH_SIZE     = 25
ANIM_INTERVAL  = 500  # ms
PYTHON_VERSIONS = [sys.executable]

class YeOldPyShoppeApp(ctk.CTk):
    # --- Core & Feature Methods ---
    def _toggle_dev_mode(self):
        if not self.dev_mode:
            pwd = simpledialog.askstring("Dev Mode","Enter password:", show="*")
            if pwd != "letmein":
                return messagebox.showwarning("Dev Mode","Incorrect password.")
        self.dev_mode = not self.dev_mode
        self._save_json(DEV_MODE_FILE, {"enabled": self.dev_mode})
        self.dev_switch.select() if self.dev_mode else self.dev_switch.deselect()

    def _toggle_ai(self):
        if self.ai_toggle.get():
            if openai is None:
                if messagebox.askyesno("Install openai","Install OpenAI SDK?"):
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "openai"])
                    import importlib; importlib.reload(sys.modules.get('openai', None))
            if not self.api_key:
                key = simpledialog.askstring("API Key","Enter OpenAI API Key:", show="*")
                if key:
                    self.api_key = key.strip()
                    openai.api_key = self.api_key
                    self._save_json(CONFIG_FILE, {"api_key": self.api_key})
        messagebox.showinfo("AI Advisor", "Enabled" if self.ai_toggle.get() else "Disabled")

    def _pick_folder(self):
        folder = filedialog.askdirectory(title="Select Watch Folder")
        if not folder:
            return
        self.watch_folder = folder
        self.watch_display.configure(state="normal")
        self.watch_display.delete(0, tk.END)
        self.watch_display.insert(0, folder)
        self.watch_display.configure(state="readonly")
        self._scan_folder(folder)

    def _scan_folder(self, folder):
        # Clear previous items
        self.results.clear(); self.batch_index = 0
        self.results_frame.destroy()
        self.results_frame = ctk.CTkScrollableFrame(self)
        self.results_frame.pack(side="left", fill="both", expand=True, padx=5)
        # Populate explorer tree
        self.tree.delete(*self.tree.get_children())
        root = self.tree.insert('', 'end', text=os.path.basename(folder), values=('Folder','',''))
        for fname in os.listdir(folder):
            fpath = os.path.join(folder, fname)
            ftype = 'Folder' if os.path.isdir(fpath) else 'File'
            size = os.path.getsize(fpath) if os.path.isfile(fpath) else ''
            mtime = time.strftime('%Y-%m-%d %H:%M', time.localtime(os.path.getmtime(fpath)))
            self.tree.insert(root, 'end', text=fname, values=(ftype, size, mtime))
            if fname.endswith('.py'):
                self.results.append(fpath)
        self.load_more_btn.configure(state='normal')

    def _load_more(self):
        end = min(self.batch_index + BATCH_SIZE, len(self.results))
        for p in self.results[self.batch_index:end]:
            self._show_file(p)
        self.batch_index = end
        if self.batch_index >= len(self.results):
            self.load_more_btn.configure(state='disabled')

    def _show_file(self, path):
        fr = ctk.CTkFrame(self.results_frame, corner_radius=5)
        fr.pack(fill='x', pady=2, padx=5)
        clr = 'black' if self.dev_mode else 'gray'
        ctk.CTkLabel(fr, text=path, text_color=clr).pack(side='left', fill='x', expand=True)
        for label, cmd, tip in [
            ('🧺 Add', lambda p=path: self._add_to_bucket(p), 'Add to Bucket'),
            ('🔍 Inspect', lambda p=path: self._extract(p), 'Inspect Classes'),
            ('VS Code', lambda p=path: self._open_vscode(p), 'Open in VS Code'),
            ('CMD', lambda p=path: self._open_cmd(p), 'Open CMD Here'),
            ('PS', lambda p=path: self._open_ps(p), 'Open PowerShell Here'),
            ('Admin', lambda p=path: self._open_admin(p), 'Elevated CMD')
        ]:
            btn = ctk.CTkButton(fr, text=label, width=60, command=cmd)
            btn.pack(side='right', padx=2)
            btn.bind('<Enter>', lambda e, t=tip: self._show_tooltip(e.widget, t))
            btn.bind('<Leave>', lambda e: self._hide_tooltip())

    # ... remaining methods unchanged ...
