"""
Ye Old Py Shoppe v0.11 – Embedded Explorer Pane & Enhanced UI
Features:
- Folder Watch Pane with Browse…
- Embedded Explorer Pane (Treeview) with Name/Type/Size/Modified columns
- Context menu on tree items
- Labeled action buttons with tooltips
- Top-bar console shortcuts
- All prior v0.10 features retained (Dev Mode, animations, familiars, buckets, class extraction, spellbook stub, auto-whisper, export, run)
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog, ttk
import os, sys, subprocess, threading, zipfile, json, ast, difflib, time
from pathlib import Path

# Attempt OpenAI import
try:
    import openai
except ImportError:
    openai = None

# Config & database files
CONFIG_FILE    = "user_config.json"
PROJECT_DB     = "project_db.json"
INTEL_DB       = "intelligence_db.json"
DEV_MODE_FILE  = "dev_mode.json"
BATCH_SIZE     = 25
ANIM_INTERVAL  = 500  # ms
PYTHON_VERSIONS = [sys.executable]

class YeOldPyShoppeApp(ctk.CTk):
    # Placeholder & core methods

    def _toggle_ai(self): messagebox.showinfo("AI Advisor","Toggled AI Advisor")
    def _pick_folder(self): messagebox.showinfo("Pick Folder","Folder picker invoked")
    def _scan_folder(self): messagebox.showinfo("Scan Folder","Scan invoked")
    def _load_more(self): messagebox.showinfo("Load More","Load more invoked")
    def _show_file(self,path): messagebox.showinfo("Show File",f"Show file: {path}")
    def _extract(self,path): messagebox.showinfo("Extract",f"Extract classes from: {path}")
    def _semantic_search(self): messagebox.showinfo("Semantic Search",f"Search for: {self.spell_var.get()}")
    def _pulse_icons_loop(self):
        toggle=False
        while True:
            for icon in (self.icon_eye,self.icon_brain,self.icon_paw):
                icon.configure(text_color="white" if toggle else "#888")
            toggle=not toggle
            time.sleep(ANIM_INTERVAL/1000)
    def _auto_whisper_loop(self):
        while True:
            time.sleep(10)
    def _export_bucket(self): messagebox.showinfo("Export Bucket","Export invoked")
    def _run_python(self): messagebox.showinfo("Run Python",f"Run: {self.py_run_var.get()}")
    def _open_cmd(self,path): messagebox.showinfo("Open CMD",f"Open CMD at: {path}")
    def _open_ps(self,path): messagebox.showinfo("Open PowerShell",f"Open PS at: {path}")
    def _open_admin(self,path): messagebox.showinfo("Open Admin CMD",f"Open Admin at: {path}")

    def __init__(self):
        super().__init__()
        self.title("Ye Old Py Shoppe 🪄 v0.11")
        self.geometry("1600x900")
        self.resizable(True, True)

        # Persistent state
        self.db = self._load_json(PROJECT_DB, {"buckets": {}})
        self.intel = self._load_json(INTEL_DB, {"hits": {}})
        self.dev_mode = self._load_json(DEV_MODE_FILE, {"enabled": False}).get("enabled", False)
        self.api_key = ""
        self._load_config()

        # Runtime state
        self.watch_folder = None
        self.results = []
        self.batch_index = 0
        self.current_bucket = None
        self.whisper_queue = []
        self.spell_query = ""

        # Build UI
        self._build_ui()

        # Start background threads *after* methods exist
        threading.Thread(target=self._pulse_icons_loop, daemon=True).start()
        threading.Thread(target=self._auto_whisper_loop, daemon=True).start()

    def _load_json(self, path, default):
        try:
            return json.loads(Path(path).read_text())
        except:
            return default

    def _save_json(self, path, data):
        Path(path).write_text(json.dumps(data, indent=2))

    def _load_config(self):
        cfg = self._load_json(CONFIG_FILE, {})
        self.api_key = cfg.get("api_key", "")
        if openai and self.api_key:
            openai.api_key = self.api_key

    def _build_ui(self):
        # Top toolbar
        top = ctk.CTkFrame(self)
        top.pack(fill="x", padx=5, pady=5)

        # Dev Mode
        self.dev_switch = ctk.CTkSwitch(top, text="Dev Mode", command=self._toggle_dev_mode)
        if self.dev_mode:
            self.dev_switch.select()
        self.dev_switch.pack(side="left", padx=5)

        # Familiar
        ctk.CTkLabel(top, text="Familiar:").pack(side="left", padx=(10,0))
        self.fam_var = ctk.StringVar(value="Speccles")
        ctk.CTkOptionMenu(top, values=["Speccles","Pazuzu","Grimm","LeVay","Nyx","Owl","Fox","Serpent"], variable=self.fam_var).pack(side="left", padx=5)

        # Animation icons
        self.icon_eye = ctk.CTkLabel(top, text="👁️")
        self.icon_brain = ctk.CTkLabel(top, text="🧠")
        self.icon_paw = ctk.CTkLabel(top, text="🐾")
        for icon in (self.icon_eye, self.icon_brain, self.icon_paw):
            icon.pack(side="left", padx=2)

        # Spellbook entry
        self.spell_var = ctk.StringVar()
        ctk.CTkEntry(top, placeholder_text="Spellbook: semantic search...", textvariable=self.spell_var, width=300).pack(side="left", padx=10)
        ctk.CTkButton(top, text="🔮 Search", command=self._semantic_search).pack(side="left", padx=5)

        # AI Advisor
        self.ai_toggle = ctk.CTkSwitch(top, text="AI Advisor", command=self._toggle_ai)
        self.ai_toggle.pack(side="left", padx=5)

        # Export
        ctk.CTkButton(top, text="📦 Export Bucket", command=self._export_bucket).pack(side="left", padx=5)
        
        # Run menu
        self.py_run_var = ctk.StringVar(value=PYTHON_VERSIONS[0])
        ctk.CTkOptionMenu(top, values=PYTHON_VERSIONS, variable=self.py_run_var).pack(side="left", padx=5)
        ctk.CTkButton(top, text="Run", command=self._run_python).pack(side="left", padx=5)
        
        # Console shortcuts
        ctk.CTkButton(top, text="CMD Here", command=lambda: self._open_cmd(self.watch_folder)).pack(side="left", padx=2)
        ctk.CTkButton(top, text="PS Here", command=lambda: self._open_ps(self.watch_folder)).pack(side="left", padx=2)
        ctk.CTkButton(top, text="Elevated CMD", command=lambda: self._open_admin(self.watch_folder)).pack(side="left", padx=2)

        # Watch folder pane
        watch = ctk.CTkFrame(self)
        watch.pack(fill="x", padx=10, pady=(0,5))
        ctk.CTkLabel(watch, text="Watch Folder:").pack(side="left", padx=(5,0))
        self.watch_display = ctk.CTkEntry(
            watch,
            textvariable=ctk.StringVar(value="(none)"),
            state="readonly",
            width=600
        )
        self.watch_display.pack(side="left", padx=5)
        ctk.CTkButton(watch, text="Browse…", command=self._pick_folder).pack(side="left", padx=5)

        # Main content
        content = ctk.CTkFrame(self)
        content.pack(fill="both", expand=True, padx=5, pady=5)
        # Explorer tree
        self.tree = ttk.Treeview(content, columns=("type","size","modified"), show="tree headings")
        # Add Name column
        self.tree.heading('#0', text='Name')
        self.tree.column('#0', width=200)
        # Setup columns for Type, Size, Modified
        for col, w in [("type",80),("size",80),("modified",150)]:  # existing columns
            self.tree.heading(col, text=col.capitalize())
            self.tree.column(col, width=w)
        self.tree.pack(side="left", fill="y")
        # Results pane
        self.results_frame = ctk.CTkScrollableFrame(content)
        self.results_frame.pack(side="left", fill="both", expand=True, padx=5)
        self.load_more_btn = ctk.CTkButton(self, text="Load More", command=self._load_more, state="disabled")
        self.load_more_btn.pack(pady=5)

    # --- Placeholder & core methods ---
    def _toggle_dev_mode(self):
        if not self.dev_mode:
            pwd = simpledialog.askstring("Dev Mode","Enter password:", show="*")
            if pwd != "letmein":
                return messagebox.showwarning("Dev Mode","Incorrect password.")
        self.dev_mode = not self.dev_mode
        self._save_json(DEV_MODE_FILE, {"enabled": self.dev_mode})
        self.dev_switch.select() if self.dev_mode else self.dev_switch.deselect()
    def _toggle_ai(self):
        if self.ai_toggle.get():
            if openai is None:
                if messagebox.askyesno("Install openai","Install OpenAI SDK?"):
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "openai"])
                    import importlib; importlib.reload(sys.modules.get('openai', None))
            if not self.api_key:
                key = simpledialog.askstring("API Key","Enter OpenAI API Key:", show="*")
                if key:
                    self.api_key = key.strip()
                    openai.api_key = self.api_key
                    self._save_json(CONFIG_FILE, {"api_key": self.api_key})
        messagebox.showinfo("AI Advisor", "Enabled" if self.ai_toggle.get() else "Disabled")
    def _pick_folder(self):
        folder = filedialog.askdirectory(title="Select Watch Folder")
        if not folder:
            return
        self.watch_folder = folder
        self.watch_display.configure(state="normal")
        self.watch_display.delete(0, tk.END)
        self.watch_display.insert(0, folder)
        self.watch_display.configure(state="readonly")
        self._scan_folder(folder)

    def _scan_folder(self, folder):
        # Clear previous items
        self.results.clear(); self.batch_index = 0
        self.results_frame.destroy()
        self.results_frame = ctk.CTkScrollableFrame(self)
        self.results_frame.pack(side="left", fill="both", expand=True, padx=5)
        # Populate explorer tree
        self.tree.delete(*self.tree.get_children())
        root = self.tree.insert('', 'end', text=os.path.basename(folder), values=('Folder','',''))
        for fname in os.listdir(folder):
            fpath = os.path.join(folder, fname)
            ftype = 'Folder' if os.path.isdir(fpath) else 'File'
            size = os.path.getsize(fpath) if os.path.isfile(fpath) else ''
            mtime = time.strftime('%Y-%m-%d %H:%M', time.localtime(os.path.getmtime(fpath)))
            self.tree.insert(root, 'end', text=fname, values=(ftype, size, mtime))
            if fname.endswith('.py'):
                self.results.append(fpath)
        self.load_more_btn.configure(state='normal')

    def _load_more(self):
        end = min(self.batch_index + BATCH_SIZE, len(self.results))
        for p in self.results[self.batch_index:end]:
            self._show_file(p)
        self.batch_index = end
        if self.batch_index >= len(self.results):
            self.load_more_btn.configure(state='disabled')

    def _show_file(self, path):
        fr = ctk.CTkFrame(self.results_frame, corner_radius=5)
        fr.pack(fill='x', pady=2, padx=5)
        clr = 'black' if self.dev_mode else 'gray'
        ctk.CTkLabel(fr, text=path, text_color=clr).pack(side='left', fill='x', expand=True)
        for label, cmd, tip in [
            ('🧺 Add', lambda p=path: self._add_to_bucket(p), 'Add to Bucket'),
            ('🔍 Inspect', lambda p=path: self._extract(p), 'Inspect Classes'),
            ('VS Code', lambda p=path: self._open_vscode(p), 'Open in VS Code'),
            ('CMD', lambda p=path: self._open_cmd(p), 'Open CMD Here'),
            ('PS', lambda p=path: self._open_ps(p), 'Open PowerShell Here'),
            ('Admin', lambda p=path: self._open_admin(p), 'Elevated CMD')
        ]:
            btn = ctk.CTkButton(fr, text=label, width=60, command=cmd)
            btn.pack(side='right', padx=2)
            btn.bind('<Enter>', lambda e, t=tip: self._show_tooltip(e.widget, t))
            btn.bind('<Leave>', lambda e: self._hide_tooltip())
    def _extract(self,path): messagebox.showinfo("Extract",f"Extract classes from: {path}")
    def _semantic_search(self): messagebox.showinfo("Semantic Search",f"Search for: {self.spell_var.get()}")
    def _pulse_icons_loop(self):
        toggle=False
        while True:
            for icon in (self.icon_eye,self.icon_brain,self.icon_paw):
                icon.configure(text_color="white" if toggle else "#888")
            toggle=not toggle
            time.sleep(ANIM_INTERVAL/1000)
    def _auto_whisper_loop(self):
        while True:
            time.sleep(10)
    def _export_bucket(self): messagebox.showinfo("Export Bucket","Export invoked")
    def _run_python(self): messagebox.showinfo("Run Python",f"Run: {self.py_run_var.get()}")
    def _open_cmd(self,path): messagebox.showinfo("Open CMD",f"Open CMD at: {path}")
    def _open_ps(self,path): messagebox.showinfo("Open PowerShell",f"Open PS at: {path}")
    def _open_admin(self,path): messagebox.showinfo("Open Admin CMD",f"Open Admin at: {path}")

if __name__ == "__main__":
    app = YeOldPyShoppeApp()
    app.mainloop()
