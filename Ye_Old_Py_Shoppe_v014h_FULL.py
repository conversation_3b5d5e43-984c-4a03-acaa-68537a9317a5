"""Ye Old Py Shoppe v0.14h – Familiar Comments, Undo, and Dev Tiered Logging
- Add docstring-style comments per familiar
- Preserve .bak for undo
- Strip comments safely
- Inject tiered dev logging
"""

import customtkinter as ctk
from tkinter import filedialog, messagebox, simpledialog, ttk
from pathlib import Path
import os, sys, json, ast, tokenize, io, shutil, datetime

class YeOldPyShoppeApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("Ye Old Py Shoppe 🪄 v0.14h")
        self.geometry("1400x900")
        self.db = self._load_json("project_db.json", {"buckets": {}})
        self.current_bucket = None
        self.bucket_btns = {}
        self.watch_folder = None
        self.results = []
        self.batch_index = 0
        self.function_cache = {}
        self.dev_mode = True
        self._build_ui()

    def _log(self, msg, level=1):
        if not self.dev_mode:
            return
        levels = {1: "[INFO]", 2: "[DEBUG]", 3: "[WARN]", 4: "[ERROR]"}
        print(f"{levels.get(level, '[LOG]')} {msg}")

    def _build_ui(self):
        toolbar = ctk.CTkFrame(self)
        toolbar.pack(fill="x", padx=5, pady=3)
        ctk.CTkButton(toolbar, text="📁 Pick Folder", command=self._pick_folder).pack(side="left", padx=2)
        ctk.CTkButton(toolbar, text="📂 Export ZIP", command=self._export_bucket).pack(side="left", padx=2)
        ctk.CTkButton(toolbar, text="💬 Add Comment", command=self._add_comment).pack(side="left", padx=2)
        ctk.CTkButton(toolbar, text="❌ Strip Comments", command=self._strip_comments).pack(side="left", padx=2)
        ctk.CTkButton(toolbar, text="🔁 Undo Comment", command=self._undo_comment).pack(side="left", padx=2)
        ctk.CTkLabel(toolbar, text="  🐾 Familiar Mode Active").pack(side="left", padx=10)

        sidebar = ctk.CTkFrame(self, width=200)
        sidebar.pack(side="left", fill="y", padx=(5,0))
        ctk.CTkLabel(sidebar, text="Buckets:", anchor="w").pack(fill="x", padx=5, pady=(5,0))
        self.bucket_panel = ctk.CTkScrollableFrame(sidebar)
        self.bucket_panel.pack(fill="both", expand=True, padx=5)
        ctk.CTkButton(sidebar, text="➕ New Bucket", command=self._new_bucket).pack(fill="x", padx=5, pady=5)
        self._refresh_buckets()

        self.content = ctk.CTkFrame(self)
        self.content.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        self.result_area = ctk.CTkScrollableFrame(self.content)
        self.result_area.pack(fill="both", expand=True)
        self._update_toolbit_view()

    def _refresh_buckets(self):
        for widget in self.bucket_panel.winfo_children():
            widget.destroy()
        self.bucket_btns.clear()
        for name in self.db.get("buckets", {}):
            btn = ctk.CTkButton(self.bucket_panel, text=name, command=lambda n=name: self._select_bucket(n))
            btn.pack(fill="x", pady=2)
            self.bucket_btns[name] = btn

    def _new_bucket(self):
        name = simpledialog.askstring("New Bucket", "Name your new bucket:")
        if name and name not in self.db["buckets"]:
            self.db["buckets"][name] = []
            self._save_json("project_db.json", self.db)
            self._refresh_buckets()

    def _select_bucket(self, name):
        self.current_bucket = name
        for btn_name, btn in self.bucket_btns.items():
            btn.configure(fg_color=("#444" if btn_name == name else None))
        self._update_toolbit_view()

    def _add_to_bucket(self, path):
        if not self.current_bucket:
            messagebox.showwarning("No Bucket", "Please select a bucket first.")
            return
        bucket = self.db.get("buckets", {}).get(self.current_bucket, [])
        if path not in bucket:
            bucket.append(path)
            self.db["buckets"][self.current_bucket] = bucket
            self._save_json("project_db.json", self.db)

    def _update_toolbit_view(self):
        for widget in self.result_area.winfo_children():
            widget.destroy()
        bucket_files = self.db.get("buckets", {}).get(self.current_bucket, [])
        for path in bucket_files:
            if Path(path).exists() and path.endswith(".py"):
                fr = ctk.CTkFrame(self.result_area)
                fr.pack(fill="x", padx=5, pady=2)
                label = ctk.CTkLabel(fr, text=path, anchor="w")
                label.pack(side="left", fill="x", expand=True)
                ctk.CTkButton(fr, text="👁️ Preview", width=80, command=lambda p=path: self._preview_classes(p)).pack(side="right", padx=2)

    def _preview_classes(self, path):
        try:
            source = Path(path).read_text(encoding='utf-8')
            tree = ast.parse(source)
            classes, methods, count = [], [], 1
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    classes.append(f"[Class_{count:02d}] {node.name}"); count += 1
                elif isinstance(node, ast.FunctionDef):
                    methods.append(f"[Func_{count:02d}] {node.name}"); count += 1
            cache_dir = Path(".cache"); cache_dir.mkdir(exist_ok=True)
            with open(cache_dir / f"{Path(path).stem}_map.json", "w") as f:
                json.dump({"classes": classes, "methods": methods}, f, indent=2)
            msg = f"Classes:\n" + "\n".join(classes) + f"\n\nMethods:\n" + "\n".join(methods)
            messagebox.showinfo("Structure", msg)
        except Exception as e:
            self._log(f"Preview error: {e}", 4)
            messagebox.showerror("Error", str(e))

    def _pick_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.watch_folder = folder
            messagebox.showinfo("Watch Folder Set", folder)

    def _export_bucket(self):
        messagebox.showinfo("Export", "[Placeholder] Exporting bucket...")

    def _add_comment(self):
        if not self.current_bucket:
            return
        for path in self.db.get("buckets", {}).get(self.current_bucket, []):
            if path.endswith(".py") and Path(path).exists():
                src = Path(path).read_text()
                backup = Path(path).with_suffix(".bak")
                shutil.copy(path, backup)
                try:
                    lines = src.splitlines()
                    out = []
                    for line in lines:
                        if line.strip().startswith("def ") or line.strip().startswith("class "):
                            out.append("    """Added by familiar."""")
                        out.append(line)
                    Path(path).write_text("\n".join(out))
                    self._log(f"Comment added to {path}", 2)
                except Exception as e:
                    self._log(f"Commenting failed: {e}", 4)

    def _strip_comments(self):
        if not self.current_bucket:
            return
        for path in self.db.get("buckets", {}).get(self.current_bucket, []):
            try:
                tokens = tokenize.generate_tokens(io.StringIO(Path(path).read_text()).readline)
                new_lines = [t.string for t in tokens if t.type != tokenize.COMMENT]
                Path(path).write_text(" ".join(new_lines))
                self._log(f"Comments stripped from {path}", 2)
            except Exception as e:
                self._log(f"Comment stripping failed: {e}", 4)

    def _undo_comment(self):
        for path in self.db.get("buckets", {}).get(self.current_bucket, []):
            bak = Path(path).with_suffix(".bak")
            if bak.exists():
                Path(path).write_text(bak.read_text())
                self._log(f"Undo restored: {path}", 1)

    def _save_json(self, path, data):
        Path(path).write_text(json.dumps(data, indent=2))

if __name__ == "__main__":
    app = YeOldPyShoppeApp()
    app.mainloop()
