"""
Comment management functionality for Phoenix application.
"""

import ast
import io
import tokenize
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from enum import Enum

from ..utils.file_utils import create_backup, restore_from_backup, safe_read_file, safe_write_file

logger = logging.getLogger(__name__)


class CommentStyle(Enum):
    """Different comment styles based on familiar personalities."""
    PHOENIX = "phoenix"
    GREY_MAN = "grey_man"
    GRIMM = "grimm"
    LEVAY = "levay"
    TECHNICAL = "technical"


class CommentManager:
    """Manages comment operations on Python files."""
    
    def __init__(self, style: CommentStyle = CommentStyle.PHOENIX):
        self.style = style
        self.backup_dir = Path(".phoenix_backups")
        self.backup_dir.mkdir(exist_ok=True)
    
    def add_comments(self, file_path: Path, comment_type: str = "docstring") -> bool:
        """Add comments to a Python file.
        
        Args:
            file_path: Path to the Python file
            comment_type: Type of comments to add ('docstring', 'inline', 'header')
            
        Returns:
            True if comments were added successfully
        """
        try:
            content = safe_read_file(file_path)
            if content is None:
                return False
            
            # Create backup
            backup_path = create_backup(file_path, self.backup_dir)
            logger.info(f"Created backup: {backup_path}")
            
            # Parse the file
            tree = ast.parse(content)
            lines = content.splitlines()
            
            # Add comments based on type
            if comment_type == "docstring":
                modified_lines = self._add_docstring_comments(tree, lines)
            elif comment_type == "inline":
                modified_lines = self._add_inline_comments(tree, lines)
            elif comment_type == "header":
                modified_lines = self._add_header_comments(lines)
            else:
                logger.warning(f"Unknown comment type: {comment_type}")
                return False
            
            # Write modified content
            modified_content = '\n'.join(modified_lines)
            return safe_write_file(file_path, modified_content, create_backup=False)
            
        except Exception as e:
            logger.error(f"Failed to add comments to {file_path}: {e}")
            return False
    
    def strip_comments(self, file_path: Path, preserve_docstrings: bool = True) -> bool:
        """Strip comments from a Python file using tokenization.
        
        Args:
            file_path: Path to the Python file
            preserve_docstrings: Whether to preserve docstrings
            
        Returns:
            True if comments were stripped successfully
        """
        try:
            content = safe_read_file(file_path)
            if content is None:
                return False
            
            # Create backup
            backup_path = create_backup(file_path, self.backup_dir)
            logger.info(f"Created backup: {backup_path}")
            
            # Use tokenization to safely remove comments
            tokens = list(tokenize.generate_tokens(io.StringIO(content).readline))
            new_tokens = []
            
            for token in tokens:
                if token.type == tokenize.COMMENT:
                    # Skip comment tokens
                    continue
                elif (token.type == tokenize.STRING and 
                      not preserve_docstrings and 
                      self._is_docstring(token, tokens)):
                    # Skip docstring tokens if not preserving them
                    continue
                else:
                    new_tokens.append(token)
            
            # Reconstruct the code
            cleaned_content = tokenize.untokenize(new_tokens)
            return safe_write_file(file_path, cleaned_content, create_backup=False)
            
        except Exception as e:
            logger.error(f"Failed to strip comments from {file_path}: {e}")
            return False
    
    def undo_last_change(self, file_path: Path) -> bool:
        """Undo the last change by restoring from the most recent backup.
        
        Args:
            file_path: Path to the file to restore
            
        Returns:
            True if restoration was successful
        """
        try:
            # Find the most recent backup for this file
            file_stem = file_path.stem
            backup_pattern = f"{file_stem}_*.bak"
            
            backup_files = list(self.backup_dir.glob(backup_pattern))
            if not backup_files:
                logger.warning(f"No backup found for {file_path}")
                return False
            
            # Sort by modification time, most recent first
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            most_recent_backup = backup_files[0]
            
            return restore_from_backup(most_recent_backup, file_path)
            
        except Exception as e:
            logger.error(f"Failed to undo changes for {file_path}: {e}")
            return False
    
    def _add_docstring_comments(self, tree: ast.AST, lines: List[str]) -> List[str]:
        """Add docstring comments to classes and functions."""
        modified_lines = lines.copy()
        
        # Find all classes and functions
        elements = []
        for node in ast.walk(tree):
            if isinstance(node, (ast.ClassDef, ast.FunctionDef)):
                elements.append(node)
        
        # Sort by line number in reverse order to avoid line number shifts
        elements.sort(key=lambda x: x.lineno, reverse=True)
        
        for element in elements:
            # Check if it already has a docstring
            if ast.get_docstring(element):
                continue
            
            # Generate comment based on style
            comment = self._generate_comment(element)
            
            # Find the insertion point (after the definition line)
            insert_line = element.lineno  # Line after the def/class line
            
            # Insert the docstring
            indent = self._get_indent(modified_lines[element.lineno - 1])
            docstring_lines = [
                f'{indent}    """',
                f'{indent}    {comment}',
                f'{indent}    """'
            ]
            
            # Insert in reverse order
            for i, docstring_line in enumerate(docstring_lines):
                modified_lines.insert(insert_line + i, docstring_line)
        
        return modified_lines
    
    def _add_inline_comments(self, tree: ast.AST, lines: List[str]) -> List[str]:
        """Add inline comments to complex statements."""
        modified_lines = lines.copy()
        
        # This is a simplified implementation
        # In a full implementation, you'd analyze the AST for complex statements
        for i, line in enumerate(modified_lines):
            stripped = line.strip()
            if (stripped.startswith('if ') or 
                stripped.startswith('for ') or 
                stripped.startswith('while ') or
                stripped.startswith('with ')):
                
                if '#' not in line:  # Don't add if already has a comment
                    comment = self._generate_inline_comment(stripped)
                    modified_lines[i] = f"{line}  # {comment}"
        
        return modified_lines
    
    def _add_header_comments(self, lines: List[str]) -> List[str]:
        """Add header comments to the file."""
        header_comment = self._generate_header_comment()
        header_lines = [
            '"""',
            header_comment,
            '"""',
            ''
        ]
        
        # Insert at the beginning, after any shebang or encoding declarations
        insert_index = 0
        for i, line in enumerate(lines):
            if line.startswith('#!') or 'coding:' in line or 'encoding:' in line:
                insert_index = i + 1
            else:
                break
        
        return lines[:insert_index] + header_lines + lines[insert_index:]
    
    def _generate_comment(self, element: ast.AST) -> str:
        """Generate a comment based on the current style."""
        if isinstance(element, ast.ClassDef):
            return self._get_class_comment(element.name)
        elif isinstance(element, ast.FunctionDef):
            return self._get_function_comment(element.name)
        else:
            return "Added by Phoenix"
    
    def _get_class_comment(self, class_name: str) -> str:
        """Get a class comment based on the current style."""
        comments = {
            CommentStyle.PHOENIX: f"Phoenix class: {class_name} - Rising from the ashes of legacy code.",
            CommentStyle.GREY_MAN: f"Class {class_name} - Quietly efficient, unnoticed excellence.",
            CommentStyle.GRIMM: f"Class {class_name} - Dark magic contained within.",
            CommentStyle.LEVAY: f"Class {class_name} - Satanic programming at its finest.",
            CommentStyle.TECHNICAL: f"Class {class_name} - Implementation details follow."
        }
        return comments.get(self.style, comments[CommentStyle.PHOENIX])
    
    def _get_function_comment(self, func_name: str) -> str:
        """Get a function comment based on the current style."""
        comments = {
            CommentStyle.PHOENIX: f"Phoenix function: {func_name} - Reborn with purpose.",
            CommentStyle.GREY_MAN: f"Function {func_name} - Does its job without fanfare.",
            CommentStyle.GRIMM: f"Function {func_name} - Weaves dark computational spells.",
            CommentStyle.LEVAY: f"Function {func_name} - Invokes the power of the machine.",
            CommentStyle.TECHNICAL: f"Function {func_name} - Performs specified operations."
        }
        return comments.get(self.style, comments[CommentStyle.PHOENIX])
    
    def _generate_inline_comment(self, statement: str) -> str:
        """Generate an inline comment for a statement."""
        if statement.startswith('if '):
            return "Phoenix condition check"
        elif statement.startswith('for '):
            return "Phoenix iteration"
        elif statement.startswith('while '):
            return "Phoenix loop"
        elif statement.startswith('with '):
            return "Phoenix context"
        else:
            return "Phoenix operation"
    
    def _generate_header_comment(self) -> str:
        """Generate a header comment for the file."""
        return f"Phoenix-enhanced Python module\nTransformed with {self.style.value} style comments."
    
    def _get_indent(self, line: str) -> str:
        """Get the indentation of a line."""
        return line[:len(line) - len(line.lstrip())]
    
    def _is_docstring(self, token: tokenize.TokenInfo, all_tokens: List[tokenize.TokenInfo]) -> bool:
        """Check if a string token is a docstring."""
        # This is a simplified check - a full implementation would be more sophisticated
        return (token.type == tokenize.STRING and 
                token.start[1] == 0)  # Starts at beginning of line
    
    def get_backup_files(self, file_path: Path) -> List[Path]:
        """Get all backup files for a given file.
        
        Args:
            file_path: Original file path
            
        Returns:
            List of backup file paths, sorted by creation time (newest first)
        """
        file_stem = file_path.stem
        backup_pattern = f"{file_stem}_*.bak"
        
        backup_files = list(self.backup_dir.glob(backup_pattern))
        backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        return backup_files
    
    def cleanup_old_backups(self, keep_count: int = 10) -> int:
        """Clean up old backup files, keeping only the most recent ones.
        
        Args:
            keep_count: Number of backup files to keep per original file
            
        Returns:
            Number of backup files deleted
        """
        deleted_count = 0
        
        # Group backups by original file
        backup_groups: Dict[str, List[Path]] = {}
        
        for backup_file in self.backup_dir.glob("*.bak"):
            # Extract original filename from backup name
            parts = backup_file.stem.split('_')
            if len(parts) >= 2:
                original_name = '_'.join(parts[:-2])  # Remove timestamp parts
                if original_name not in backup_groups:
                    backup_groups[original_name] = []
                backup_groups[original_name].append(backup_file)
        
        # Clean up each group
        for original_name, backups in backup_groups.items():
            backups.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # Delete old backups beyond keep_count
            for backup_to_delete in backups[keep_count:]:
                try:
                    backup_to_delete.unlink()
                    deleted_count += 1
                    logger.debug(f"Deleted old backup: {backup_to_delete}")
                except Exception as e:
                    logger.error(f"Failed to delete backup {backup_to_delete}: {e}")
        
        return deleted_count
