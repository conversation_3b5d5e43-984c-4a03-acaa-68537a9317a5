"""
Toolbar for Phoenix application.
"""

import tkinter as tk
from tkinter import ttk
import logging
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .main_window import MainWindow

logger = logging.getLogger(__name__)


class Toolbar(ttk.Frame):
    """Application toolbar with common actions."""
    
    def __init__(self, parent, main_window: 'MainWindow'):
        super().__init__(parent, relief=tk.RAISED, borderwidth=1)
        self.main_window = main_window
        
        self._create_buttons()
    
    def _create_buttons(self):
        """Create toolbar buttons."""
        # File operations
        ttk.Button(self, text="📁 Open", command=self.main_window.open_folder, width=8).pack(side=tk.LEFT, padx=2, pady=2)
        ttk.Button(self, text="💾 Save", command=self.main_window.save_project, width=8).pack(side=tk.LEFT, padx=2, pady=2)
        
        # Separator
        ttk.Separator(self, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=2)
        
        # Bucket operations
        ttk.Button(self, text="🗂️ New Bucket", command=self.main_window.new_bucket, width=12).pack(side=tk.LEFT, padx=2, pady=2)
        ttk.Button(self, text="📦 Export", command=self.export_current_bucket, width=8).pack(side=tk.LEFT, padx=2, pady=2)
        
        # Separator
        ttk.Separator(self, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=2)
        
        # Analysis operations
        ttk.Button(self, text="🔍 Analyze", command=self.analyze_selected, width=10).pack(side=tk.LEFT, padx=2, pady=2)
        ttk.Button(self, text="⟳ Refresh", command=self.main_window.refresh_all, width=10).pack(side=tk.LEFT, padx=2, pady=2)
        
        # Separator
        ttk.Separator(self, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=2)
        
        # Comment operations
        ttk.Button(self, text="💬 Add Comments", command=self.add_comments_to_selected, width=15).pack(side=tk.LEFT, padx=2, pady=2)
        ttk.Button(self, text="🗑️ Strip Comments", command=self.strip_comments_from_selected, width=15).pack(side=tk.LEFT, padx=2, pady=2)
        ttk.Button(self, text="↶ Undo", command=self.undo_selected, width=8).pack(side=tk.LEFT, padx=2, pady=2)
        
        # Right-aligned status
        self.status_label = ttk.Label(self, text="Ready", foreground="blue")
        self.status_label.pack(side=tk.RIGHT, padx=10, pady=2)
    
    def export_current_bucket(self):
        """Export the currently selected bucket."""
        if self.main_window.current_bucket:
            self.main_window.export_bucket(self.main_window.current_bucket)
        else:
            from tkinter import messagebox
            messagebox.showwarning("Warning", "Please select a bucket to export")
    
    def analyze_selected(self):
        """Analyze the selected file."""
        # Try to get selected file from left browser
        if self.main_window.left_browser:
            file_path = self.main_window.left_browser.get_selected_file()
            if file_path and file_path.suffix == '.py':
                self.main_window.analyze_file(file_path)
                return
        
        # Try to get selected file from project panel
        if self.main_window.project_panel:
            file_path = self.main_window.project_panel.get_selected_file()
            if file_path and file_path.suffix == '.py':
                self.main_window.analyze_file(file_path)
                return
        
        from tkinter import messagebox
        messagebox.showwarning("Warning", "Please select a Python file to analyze")
    
    def add_comments_to_selected(self):
        """Add comments to the selected file."""
        file_path = self._get_selected_python_file()
        if file_path:
            self.main_window.add_comments_to_file(file_path)
        else:
            from tkinter import messagebox
            messagebox.showwarning("Warning", "Please select a Python file")
    
    def strip_comments_from_selected(self):
        """Strip comments from the selected file."""
        file_path = self._get_selected_python_file()
        if file_path:
            self.main_window.strip_comments_from_file(file_path)
        else:
            from tkinter import messagebox
            messagebox.showwarning("Warning", "Please select a Python file")
    
    def undo_selected(self):
        """Undo changes to the selected file."""
        file_path = self._get_selected_file()
        if file_path:
            self.main_window.undo_last_change(file_path)
        else:
            from tkinter import messagebox
            messagebox.showwarning("Warning", "Please select a file")
    
    def _get_selected_file(self):
        """Get the currently selected file from any panel."""
        # Try left browser first
        if self.main_window.left_browser:
            file_path = self.main_window.left_browser.get_selected_file()
            if file_path:
                return file_path
        
        # Try project panel
        if self.main_window.project_panel:
            file_path = self.main_window.project_panel.get_selected_file()
            if file_path:
                return file_path
        
        return None
    
    def _get_selected_python_file(self):
        """Get the currently selected Python file."""
        file_path = self._get_selected_file()
        if file_path and file_path.suffix == '.py':
            return file_path
        return None
    
    def set_status(self, message: str, color: str = "blue"):
        """Set the status message in the toolbar."""
        self.status_label.config(text=message, foreground=color)
