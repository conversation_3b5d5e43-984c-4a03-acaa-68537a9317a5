
"""
Ye Old Py Shoppe v0.05 – Speccles Speaks
Includes:
- All v0.04c features
- OpenAI ChatGPT integration for "Ask Speccles"
- Settings dialog to set/clear API key
- Stored in user_config.json
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import os, hashlib, zipfile, threading, subprocess, ast, json
from pathlib import Path
import openai

# Constants
BATCH_SIZE = 25
CONFIG_FILE = "user_config.json"

class YeOldPyShoppeApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Ye Old Py Shoppe 🪄 v0.05")
        self.geometry("1300x800")
        self.resizable(False, False)

        # Data structures
        self.results = []
        self.batch_index = 0
        self.whisper_queue = []
        self.buckets = {}
        self.favorites = []
        self.api_key = ""
        self.load_config()

        # UI Build
        self.build_ui()

    def build_ui(self):
        # Menu
        menubar = tk.Menu(self)
        settings_menu = tk.Menu(menubar, tearoff=0)
        settings_menu.add_command(label="Set API Key", command=self.set_api_key)
        settings_menu.add_command(label="Clear API Key", command=self.clear_api_key)
        menubar.add_cascade(label="Settings", menu=settings_menu)
        self.config(menu=menubar)

        # Top Frame
        top_frame = tk.Frame(self)
        top_frame.pack(fill="x", padx=5, pady=5)

        # Familiar Selector
        tk.Label(top_frame, text="Familiar:").pack(side="left")
        self.current_familiar = tk.StringVar(value="Speccles")
        fam_combo = ttk.Combobox(top_frame, textvariable=self.current_familiar,
                                 values=["Speccles", "Pazuzu", "Grimm", "LeVay", "Nyx"], state="readonly")
        fam_combo.pack(side="left", padx=5)

        # Add Folder
        tk.Button(top_frame, text="📂 Add Folder", command=self.add_folder).pack(side="left", padx=5)

        # Search Bar
        self.search_var = tk.StringVar()
        tk.Entry(top_frame, textvariable=self.search_var, width=30).pack(side="left", padx=5)
        tk.Button(top_frame, text="Ask Speccles", command=self.ask_speccles).pack(side="left", padx=5)

        # Bucket controls
        tk.Label(top_frame, text="Bucket:").pack(side="left", padx=(20,0))
        self.bucket_var = tk.StringVar()
        self.bucket_combo = ttk.Combobox(top_frame, textvariable=self.bucket_var, values=list(self.buckets.keys()))
        self.bucket_combo.pack(side="left", padx=5)
        tk.Button(top_frame, text="New Bucket", command=self.new_bucket).pack(side="left", padx=5)

        # Favorites bar
        fav_frame = tk.Frame(self)
        fav_frame.pack(fill="x", padx=5, pady=(0,5))
        tk.Button(fav_frame, text="Add Favorite", command=self.add_favorite).pack(side="left", padx=5)
        self.fav_buttons_frame = tk.Frame(fav_frame)
        self.fav_buttons_frame.pack(side="left")

        # Results panel with scrollbar
        result_container = tk.Frame(self)
        result_container.pack(fill="both", expand=True, padx=5, pady=5)
        self.canvas = tk.Canvas(result_container)
        self.scrollbar = tk.Scrollbar(result_container, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # Load more button
        self.load_more_btn = tk.Button(self, text="Load More", command=self.load_more_results, state="disabled")
        self.load_more_btn.pack(pady=(0,5))

        # Whisper queue label
        self.whisper_label = tk.Label(self, text="🐾 Whisper Queue: 0 entries")
        self.whisper_label.pack(pady=(0,5))

    def load_config(self):
        if Path(CONFIG_FILE).exists():
            try:
                cfg = json.loads(Path(CONFIG_FILE).read_text())
                self.api_key = cfg.get("api_key", "")
            except:
                self.api_key = ""
        openai.api_key = self.api_key

    def save_config(self):
        cfg = {"api_key": self.api_key}
        with open(CONFIG_FILE, "w") as f:
            json.dump(cfg, f)

    def set_api_key(self):
        key = simpledialog.askstring("API Key", "Enter OpenAI API Key:", show="*")
        if key:
            self.api_key = key.strip()
            openai.api_key = self.api_key
            self.save_config()
            messagebox.showinfo("API Key Saved", "Your API key has been saved.")

    def clear_api_key(self):
        self.api_key = ""
        openai.api_key = ""
        self.save_config()
        messagebox.showinfo("API Key Cleared", "Your API key has been cleared.")

    def add_folder(self):
        folder = filedialog.askdirectory()
        if not folder: return
        self.results.clear()
        self.batch_index = 0
        self.whisper_queue.clear()
        self.update_whisper_label()
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        threading.Thread(target=self.scan_folder, args=(folder,), daemon=True).start()

    def scan_folder(self, folder):
        for root, dirs, files in os.walk(folder):
            for file in files:
                full_path = os.path.join(root, file)
                if file.endswith(".py"):
                    self.process_py_file(full_path)
                elif file.endswith(".zip"):
                    self.process_zip(full_path)
        self.after(0, self.load_more_results)

    def process_py_file(self, path):
        try:
            with open(path, "r", encoding="utf-8", errors="ignore") as f:
                code = f.read()
            rating = self.rate_file(code)
            self.results.append({"type": "file", "path": path, "code": code, "rating": rating})
        except Exception as e:
            self.whisper_queue.append({"path": path, "error": str(e)})
        self.after(0, self.update_whisper_label)

    def process_zip(self, zip_path):
        try:
            with zipfile.ZipFile(zip_path, 'r') as z:
                for info in z.infolist():
                    if info.filename.endswith(".py") and not info.is_dir():
                        try:
                            code = z.read(info.filename).decode("utf-8", errors="ignore")
                            fake_path = f"archive://{zip_path}!{info.filename}"
                            rating = self.rate_file(code)
                            self.results.append({"type": "zip", "path": fake_path, "code": code, "rating": rating})
                        except Exception as e:
                            self.whisper_queue.append({"path": f"{zip_path}!{info.filename}", "error": str(e)})
        except Exception as e:
            self.whisper_queue.append({"path": zip_path, "error": str(e)})
        self.after(0, self.update_whisper_label)

    def rate_file(self, code):
        try:
            ast.parse(code)
            defs = code.count("def ")
            classes = code.count("class ")
            if defs + classes == 0:
                return "Suspect"
            return "OK"
        except:
            return "Broken"

    def load_more_results(self):
        end = self.batch_index + BATCH_SIZE
        batch = self.results[self.batch_index:end]
        for item in batch:
            self.display_result(item)
        self.batch_index += BATCH_SIZE
        if self.batch_index < len(self.results):
            self.load_more_btn.config(state="normal")
        else:
            self.load_more_btn.config(state="disabled")

    def display_result(self, item):
        frame = tk.Frame(self.scrollable_frame, bd=1, relief="solid", padx=5, pady=5)
        frame.pack(fill="x", pady=2)

        tk.Label(frame, text=item["path"], font=("Consolas", 10)).pack(anchor="w")
        tk.Label(frame, text=f"Rating: {item['rating']}", fg=("green" if item["rating"]=="OK" else "orange" if item["rating"]=="Suspect" else "red")).pack(anchor="w")

        preview_text = item["code"][:200] + ("..." if len(item["code"]) > 200 else "")
        txt = tk.Text(frame, height=5, wrap="word")
        txt.insert("1.0", preview_text)
        txt.config(state="disabled")
        txt.pack(fill="x", pady=2)

        size = len(item["code"].encode("utf-8")) if "code" in item else 0
        sha = hashlib.sha256(item["code"].encode("utf-8")).hexdigest()[:8] if "code" in item else ""
        meta = f"SHA256: {sha} | Size: {size} bytes"
        tk.Label(frame, text=meta, font=("Consolas", 8), fg="gray").pack(anchor="w")

        btn_frame = tk.Frame(frame)
        btn_frame.pack(anchor="e", pady=2)

        if item["type"] == "file":
            folder = os.path.dirname(item["path"])
        else:
            folder = None

        if folder:
            tk.Button(btn_frame, text="📁 Open Folder", command=lambda p=folder: self.open_folder(p)).pack(side="left", padx=2)
            tk.Button(btn_frame, text="💻 CMD Here", command=lambda p=folder: self.open_cmd(p)).pack(side="left", padx=2)

        tk.Button(btn_frame, text="🧺 Add to Bucket", command=lambda p=item["path"]: self.add_to_bucket(p)).pack(side="left", padx=2)

    def open_folder(self, path):
        if os.name == 'nt':
            os.startfile(path)
        elif os.name == 'posix':
            subprocess.Popen(['xdg-open', path])
        else:
            messagebox.showinfo("Open Folder", f"Open folder: {path}")

    def open_cmd(self, path):
        if os.name == 'nt':
            subprocess.Popen(["cmd", "/K"], cwd=path)
        elif os.name == 'posix':
            subprocess.Popen(["x-terminal-emulator", "--working-directory", path])
        else:
            messagebox.showinfo("Open CMD", f"Open CMD at: {path}")

    def ask_speccles(self):
        query = self.search_var.get().strip()
        if not query:
            return
        if not self.api_key:
            messagebox.showwarning("No API Key", "Please set your OpenAI API key in Settings.")
            return
        # Build prompt
        persona = self.current_familiar.get()
        prompt = f"You are {persona}, the Ye Old Py Shoppe familiar. The user asks: '{query}'. Provide a helpful, concise answer."
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "system", "content": prompt}]
            )
            answer = response.choices[0].message.content.strip()
            self.results.insert("end", f"💬 {persona} says: {answer}\n")
        except Exception as e:
            messagebox.showerror("API Error", str(e))

    def new_bucket(self):
        name = filedialog.askstring("New Bucket", "Bucket name:")
        if name:
            if name in self.buckets:
                messagebox.showwarning("Bucket Exists", f"Bucket '{name}' already exists.")
            else:
                self.buckets[name] = []
                self.bucket_combo.config(values=list(self.buckets.keys()))

    def add_to_bucket(self, path):
        bucket = self.bucket_var.get()
        if not bucket:
            messagebox.showwarning("No Bucket", "Select or create a bucket first.")
            return
        if path in self.buckets[bucket]:
            messagebox.showinfo("Already in Bucket", f"'{path}' is already in '{bucket}'.")
        else:
            self.buckets[bucket].append(path)
            messagebox.showinfo("Added", f"Added to bucket '{bucket}'.")

    def add_favorite(self):
        path = filedialog.askdirectory()
        if path and path not in self.favorites:
            self.favorites.append(path)
            btn = tk.Button(self.fav_buttons_frame, text=os.path.basename(path), command=lambda p=path: self.open_folder(p))
            btn.pack(side="left", padx=2)

    def update_whisper_label(self):
        count = len(self.whisper_queue)
        self.whisper_label.config(text=f"🐾 Whisper Queue: {count} entries")

if __name__ == "__main__":
    app = YeOldPyShoppeApp()
    app.mainloop()
