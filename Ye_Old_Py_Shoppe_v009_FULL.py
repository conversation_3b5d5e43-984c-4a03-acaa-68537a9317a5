
"""
Ye Old Py Shoppe v0.09 – Spellbook & Auto-Whisper
Features:
- CustomTkinter UI framework
- Semantic natural-language Search (Spellbook stub)
- Intelligence DB for accepted/rejected hits
- Background auto-whisper of suspect snippets when AI enabled
- Export bucket to ZIP archive
- Auto-install of openai SDK on AI toggle
"""

import customtkinter as ctk
from tkinter import filedialog, messagebox, simpledialog
import os, sys, subprocess, threading, zipfile, json, ast, difflib
from pathlib import Path

# Attempt to import openai
try:
    import openai
except ImportError:
    openai = None

ctk.set_appearance_mode("System")
ctk.set_default_color_theme("blue")

# Config and database files
CONFIG_FILE = "user_config.json"
PROJECT_DB = "project_db.json"
INTEL_DB = "intelligence_db.json"
BATCH_SIZE = 25

# Python versions stub
PYTHON_VERSIONS = [sys.executable]

class YeOldPyShoppeApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("Ye Old Py Shoppe 🪄 v0.09")
        self.geometry("1400x900")
        # Load configurations
        self.db = self._load_json(PROJECT_DB, {"buckets": {}})
        self.intel = self._load_json(INTEL_DB, {"hits": {}})
        self.api_key = ""
        self._load_config()
        # State
        self.results = []
        self.batch_index = 0
        self.whisper_queue = []
        # Build UI
        self._build_ui()
        # Start auto-whisper thread
        threading.Thread(target=self._auto_whisper_loop, daemon=True).start()

    def _load_json(self, path, default):
        try:
            return json.loads(Path(path).read_text())
        except:
            return default

    def _save_json(self, path, data):
        Path(path).write_text(json.dumps(data, indent=2))

    def _load_config(self):
        cfg = self._load_json(CONFIG_FILE, {})
        self.api_key = cfg.get("api_key", "")
        if openai and self.api_key:
            openai.api_key = self.api_key

    def _save_config(self):
        self._save_json(CONFIG_FILE, {"api_key": self.api_key})

    def _build_ui(self):
        # Top frame with search and controls
        top = ctk.CTkFrame(self)
        top.pack(fill="x", padx=5, pady=5)
        # Spellbook entry
        self.spell_var = ctk.StringVar()
        ctk.CTkEntry(top, placeholder_text="Spellbook: semantic search...", textvariable=self.spell_var, width=400).pack(side="left", padx=5)
        ctk.CTkButton(top, text="🔮 Search", command=self._semantic_search).pack(side="left", padx=5)
        # AI toggle
        self.ai_toggle = ctk.CTkSwitch(top, text="AI Advisor", command=self._toggle_ai)
        self.ai_toggle.pack(side="left", padx=10)
        # Export bucket button
        ctk.CTkButton(top, text="📦 Export Bucket", command=self._export_bucket).pack(side="left", padx=5)
        # Python run menu
        self.py_run_var = ctk.StringVar(value=PYTHON_VERSIONS[0])
        ctk.CTkOptionMenu(top, values=PYTHON_VERSIONS, variable=self.py_run_var).pack(side="left", padx=5)
        ctk.CTkButton(top, text="Run", command=self._run_python).pack(side="left", padx=5)
        # Placeholder for main content (buckets/results)
        ctk.CTkLabel(self, text="... buckets and results UI ...", fg_color="transparent").pack(expand=True)

    def _toggle_ai(self):
        if self.ai_toggle.get():
            # Auto-install OpenAI SDK if missing
            if openai is None:
                if messagebox.askyesno("Install openai", "OpenAI SDK not found. Install now?"):
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "openai"])
                    import importlib
                    importlib.reload(sys.modules.get('openai', None))
            if not self.api_key:
                key = simpledialog.askstring("API Key","Enter your OpenAI API key:", show="*")
                if key:
                    self.api_key = key.strip()
                    if openai:
                        openai.api_key = self.api_key
                    self._save_config()
        messagebox.showinfo("AI Advisor", "Enabled" if self.ai_toggle.get() else "Disabled")

    def _semantic_search(self):
        query = self.spell_var.get().strip()
        if not query:
            return
        # Stub: notify
        messagebox.showinfo("Spellbook", f"Searched for '{query}' (stub)")

    def _auto_whisper_loop(self):
        while True:
            if self.ai_toggle.get() and openai and self.api_key and self.whisper_queue:
                snippet = self.whisper_queue.pop(0)
                try:
                    resp = openai.ChatCompletion.create(
                        model="gpt-3.5-turbo",
                        messages=[{"role":"system","content": snippet.get("code","")}]
                    )
                    # stub: handle response
                except:
                    pass
            threading.Event().wait(10)

    def _export_bucket(self):
        name = simpledialog.askstring("Export Bucket", "Enter bucket name to export:")
        if not name:
            return
        files = self.db.get("buckets", {}).get(name, [])
        if not files:
            messagebox.showwarning("Export", "No files in bucket.")
            return
        target = filedialog.asksaveasfilename(defaultextension=".zip", initialfile=f"{name}.zip")
        if not target:
            return
        with zipfile.ZipFile(target, "w") as zf:
            for p in files:
                if p.startswith("archive://"):
                    zp, member = p[len("archive://"):].split("!")
                    data = zipfile.ZipFile(zp).read(member)
                    zf.writestr(Path(member).name, data)
                else:
                    zf.write(p, arcname=Path(p).name)
        messagebox.showinfo("Export", f"Exported {len(files)} files to {target}")

    def _run_python(self):
        ver = self.py_run_var.get()
        messagebox.showinfo("Run", f"Would run with: {ver}")

if __name__ == "__main__":
    app = YeOldPyShoppeApp()
    app.mainloop()
