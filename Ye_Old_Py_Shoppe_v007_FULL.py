
"""
Ye Old Py Shoppe v0.07 – Buckets UI & Class Extraction
Features:
- CustomTkinter UI
- Buckets sidebar: create, select buckets
- Scan folder recursively for .py and .zip
- Display scan results
- Add files to buckets
- View bucket contents
- Class & Method extraction from selected file
- Export extracted class/method to .txt
"""

import customtkinter as ctk
from tkinter import filedialog, messagebox
import os, ast, zipfile
from pathlib import Path

ctk.set_appearance_mode("System")
ctk.set_default_color_theme("blue")

CONFIG_DB = "project_db.json"

class PyShoppeApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("Ye Old Py Shoppe v0.07 – Buckets & Extraction")
        self.geometry("1400x800")
        self.state = self.load_state()
        self.current_bucket = None
        self.scan_results = []
        self.build_ui()

    def load_state(self):
        if Path(CONFIG_DB).exists():
            import json
            return json.loads(Path(CONFIG_DB).read_text())
        return {"buckets": {}}

    def save_state(self):
        import json
        Path(CONFIG_DB).write_text(json.dumps(self.state, indent=2))

    def build_ui(self):
        # Sidebar for buckets
        self.sidebar = ctk.CTkScrollableFrame(self, width=200, corner_radius=0)
        self.sidebar.pack(side="left", fill="y")
        ctk.CTkLabel(self.sidebar, text="Buckets", font=("Helvetica", 16)).pack(pady=10)
        ctk.CTkButton(self.sidebar, text="+ New Bucket", command=self.new_bucket).pack(pady=5)
        self.bucket_btns = {}
        self.refresh_buckets()

        # Main area
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(side="left", fill="both", expand=True, padx=10, pady=10)

        # Top controls
        ctrl = ctk.CTkFrame(self.main_frame)
        ctrl.pack(fill="x")
        ctk.CTkButton(ctrl, text="📂 Scan Folder", command=self.scan_folder).pack(side="left", padx=5)
        self.label_info = ctk.CTkLabel(ctrl, text="No bucket selected", font=("Helvetica", 14))
        self.label_info.pack(side="left", padx=10)

        # Content frames
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(fill="both", expand=True, pady=10)

        # Scan results list
        self.results_list = ctk.CTkScrollableFrame(self.content_frame)
        # Bucket contents list
        self.bucket_list = ctk.CTkScrollableFrame(self.content_frame)
        self.results_list.pack(fill="both", expand=True)

    def refresh_buckets(self):
        # Clear existing
        for btn in self.bucket_btns.values():
            btn.destroy()
        self.bucket_btns.clear()
        # Create buttons
        for name in self.state["buckets"]:
            btn = ctk.CTkButton(self.sidebar, text=name, command=lambda n=name: self.select_bucket(n))
            btn.pack(fill="x", padx=5, pady=2)
            self.bucket_btns[name] = btn

    def new_bucket(self):
        name = ctk.simpledialog.askstring("New Bucket", "Enter bucket name:")
        if name:
            self.state["buckets"].setdefault(name, [])
            self.save_state()
            self.refresh_buckets()

    def select_bucket(self, name):
        self.current_bucket = name
        self.label_info.configure(text=f"Bucket: {name}")
        # Show bucket list
        for widget in self.content_frame.winfo_children():
            widget.pack_forget()
        self.bucket_list.pack(fill="both", expand=True)
        self.show_bucket_contents()

    def scan_folder(self):
        folder = filedialog.askdirectory()
        if not folder: return
        self.scan_results.clear()
        self.results_list.pack(fill="both", expand=True)
        self.bucket_list.pack_forget()
        self.show_scan_results(folder)

    def show_scan_results(self, folder):
        for w in self.results_list.winfo_children():
            w.destroy()
        # Recursive scan
        for root, dirs, files in os.walk(folder):
            for f in files:
                if f.endswith(".py"):
                    path = os.path.join(root, f)
                    self.scan_results.append(path)
                elif f.endswith(".zip"):
                    zp = os.path.join(root, f)
                    try:
                        with zipfile.ZipFile(zp) as zf:
                            for info in zf.infolist():
                                if info.filename.endswith(".py"):
                                    self.scan_results.append(f"archive://{zp}!{info.filename}")
                    except:
                        pass
        # Display
        for path in self.scan_results:
            fr = ctk.CTkFrame(self.results_list)
            fr.pack(fill="x", pady=2, padx=5)
            ctk.CTkLabel(fr, text=path, anchor="w").pack(side="left", fill="x", expand=True)
            ctk.CTkButton(fr, text="🧺", width=30, command=lambda p=path: self.add_to_bucket(p)).pack(side="right", padx=5)
            ctk.CTkButton(fr, text="🔍", width=30, command=lambda p=path: self.extract_classes(p)).pack(side="right")

    def add_to_bucket(self, path):
        if not self.current_bucket:
            messagebox.showwarning("No Bucket", "Select a bucket first.")
            return
        self.state["buckets"][self.current_bucket].append(path)
        self.save_state()
        messagebox.showinfo("Added", f"Added to bucket '{self.current_bucket}'")

    def show_bucket_contents(self):
        for w in self.bucket_list.winfo_children():
            w.destroy()
        for path in self.state["buckets"].get(self.current_bucket, []):
            fr = ctk.CTkFrame(self.bucket_list)
            fr.pack(fill="x", pady=2, padx=5)
            ctk.CTkLabel(fr, text=path, anchor="w").pack(side="left", fill="x", expand=True)
            ctk.CTkButton(fr, text="🔍", width=30, command=lambda p=path: self.extract_classes(p)).pack(side="right", padx=5)

    def extract_classes(self, path):
        # Determine file content
        if path.startswith("archive://"):
            zp, fname = path[len("archive://"):].split("!")
            try:
                with zipfile.ZipFile(zp) as zf:
                    code = zf.read(fname).decode("utf-8", errors="ignore")
            except:
                messagebox.showerror("Error", f"Failed to read {path}")
                return
        else:
            try:
                code = open(path, "r", encoding="utf-8", errors="ignore").read()
            except:
                messagebox.showerror("Error", f"Failed to open {path}")
                return
        # Parse AST
        tree = ast.parse(code)
        classes = [n for n in tree.body if isinstance(n, ast.ClassDef)]
        if not classes:
            messagebox.showinfo("No Classes", "No class definitions found.")
            return
        # Show dialog to choose class
        names = [cls.name for cls in classes]
        sel = ctk.simpledialog.askstring("Select Class", "Available: " + ", ".join(names))
        if sel not in names:
            return
        # Extract selected class source
        for cls in classes:
            if cls.name == sel:
                start, end = cls.lineno-1, cls.end_lineno
                lines = code.splitlines()[start:end]
                out = "
".join(lines)
                # Save to file
                out_path = Path("extracted_"+sel+".txt")
                out_path.write_text(out)
                messagebox.showinfo("Extracted", f"Class '{sel}' saved to {out_path}")
                return

if __name__ == "__main__":
    app = PyShoppeApp()
    app.mainloop()
